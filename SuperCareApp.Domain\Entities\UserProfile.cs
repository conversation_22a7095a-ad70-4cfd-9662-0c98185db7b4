﻿using SuperCareApp.Domain.Common;
using SuperCareApp.Domain.Identity;

namespace SuperCareApp.Domain.Entities
{
    public class UserProfile : BaseEntity
    {
        public Guid ApplicationUserId { get; set; } // Foreign key to ApplicationUser
        public string? FirstName { get; set; } = string.Empty;
        public string? LastName { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Gender { get; set; }
        public string? ImageName { get; set; }
        public string? ImagePath { get; set; }
        public string? Country { get; set; }
        public string? Preferences { get; set; } // Stored as JSON string

        // Navigation properties
        public ApplicationUser User { get; set; } = null!;
        public ICollection<Document> Documents { get; set; } = new List<Document>();
    }
}
