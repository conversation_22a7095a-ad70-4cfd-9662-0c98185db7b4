﻿using System.Globalization;
using FluentValidation;
using Microsoft.AspNetCore.Http;

namespace SuperCareApp.Application.Common.Models.Identity;

public class UpdateUserRequest
{
    public string FirstName { get; set; } = string.Empty;

    public string LastName { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;

    public string Email { get; set; } = string.Empty;

    public string Gender { get; set; } = string.Empty;

    public int? YearsExperience { get; set; }

    public string DateOfBirth { get; set; } = string.Empty;
    public int? BufferDuration { get; set; }

    /// <summary>
    /// Profile picture file (optional)
    /// </summary>
    public IFormFile? ProfilePicture { get; set; }

    public AddressInfo? PrimaryAddress { get; set; }
}

public class UpdateUserRequestValidator : AbstractValidator<UpdateUserRequest>
{
    public UpdateUserRequestValidator()
    {
        RuleFor(x => x.FirstName)
            .MaximumLength(100)
            .WithMessage("Name cannot exceed 100 characters.")
            .When(x => !string.IsNullOrEmpty(x.FirstName));

        RuleFor(x => x.LastName)
            .MaximumLength(100)
            .WithMessage("Name cannot exceed 100 characters.")
            .When(x => !string.IsNullOrEmpty(x.LastName));

        RuleFor(x => x.PhoneNumber)
            .Matches(@"^\+?1?\d{9,15}$")
            .WithMessage("Phone number must be a valid format (e.g., +1234567890 or 1234567890).")
            .When(x => !string.IsNullOrEmpty(x.PhoneNumber));

        RuleFor(x => x.Email)
            .EmailAddress()
            .WithMessage("A valid email address is required.")
            .MaximumLength(254)
            .WithMessage("Email cannot exceed 254 characters.")
            .When(x => !string.IsNullOrEmpty(x.Email));

        RuleFor(x => x.Gender)
            .Must(g => g is "Male" or "Female" or "Other")
            .WithMessage("Gender must be Male, Female, or Other.")
            .When(x => !string.IsNullOrEmpty(x.Gender));

        RuleFor(x => x.YearsExperience)
            .InclusiveBetween(0, 100)
            .WithMessage("Years of experience must be between 0 and 100.")
            .When(x => x.YearsExperience.HasValue);

        RuleFor(x => x.DateOfBirth)
            .Must(BeValidDate)
            .WithMessage("Date of birth must be a valid date in the format YYYY-MM-DD.")
            .When(x => !string.IsNullOrEmpty(x.DateOfBirth));

        RuleFor(x => x.BufferDuration)
            .InclusiveBetween(0, 100)
            .WithMessage("Buffer duration must be between 0 and 100.")
            .When(x => x.BufferDuration.HasValue);

        RuleFor(x => x.PrimaryAddress)
            .SetValidator(new AddressInfoValidator()!)
            .When(x => x.PrimaryAddress != null);
    }

    private bool BeValidDate(string date)
    {
        return DateTime.TryParseExact(
            date,
            "yyyy-MM-dd",
            CultureInfo.InvariantCulture,
            DateTimeStyles.None,
            out _
        );
    }
}

public class AddressInfoValidator : AbstractValidator<AddressInfo>
{
    public AddressInfoValidator()
    {
        RuleFor(x => x.StreetAddress)
            .NotEmpty()
            .WithMessage("Street address is required.")
            .MaximumLength(200)
            .WithMessage("Street address cannot exceed 200 characters.");

        RuleFor(x => x.City)
            .MaximumLength(100)
            .WithMessage("City cannot exceed 100 characters.")
            .When(x => !string.IsNullOrEmpty(x.City));

        RuleFor(x => x.State)
            .MaximumLength(100)
            .WithMessage("State cannot exceed 100 characters.")
            .When(x => !string.IsNullOrEmpty(x.State));

        RuleFor(x => x.PostalCode)
            .Matches(@"^\d{5}(-\d{4})?$")
            .WithMessage("Postal code must be a valid format (e.g., 12345 or 12345-6789).")
            .When(x => !string.IsNullOrEmpty(x.PostalCode));

        RuleFor(x => x.Latitude)
            .InclusiveBetween(-90, 90)
            .WithMessage("Latitude must be between -90 and 90 degrees.")
            .When(x => x.Latitude.HasValue);

        RuleFor(x => x.Longitude)
            .InclusiveBetween(-180, 180)
            .WithMessage("Longitude must be between -180 and 180 degrees.")
            .When(x => x.Longitude.HasValue);

        RuleFor(x => x.Label)
            .MaximumLength(50)
            .WithMessage("Label cannot exceed 50 characters.")
            .When(x => !string.IsNullOrEmpty(x.Label));
    }
}
