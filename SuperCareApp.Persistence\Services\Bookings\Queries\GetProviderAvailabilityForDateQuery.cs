using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Entities.ValueObjects;

namespace SuperCareApp.Persistence.Services.Bookings.Queries;

/// <summary>
/// Query to get provider availability for a specific date
/// </summary>
/// <param name="ProviderId">The provider ID</param>
/// <param name="Date">The specific date to check availability for</param>
public record GetProviderAvailabilityForDateQuery(Guid ProviderId, DateOnly Date)
    : IQuery<Result<AvailabilityResponse>>;

/// <summary>
/// Handler for GetProviderAvailabilityForDateQuery
/// </summary>
public sealed class GetProviderAvailabilityForDateQueryHandler
    : IQueryHandler<GetProviderAvailabilityForDateQuery, Result<AvailabilityResponse>>
{
    private readonly ApplicationDbContext _db;
    private readonly ILogger<GetProviderAvailabilityForDateQueryHandler> _logger;

    public GetProviderAvailabilityForDateQueryHandler(
        ApplicationDbContext db,
        ILogger<GetProviderAvailabilityForDateQueryHandler> logger
    )
    {
        _db = db;
        _logger = logger;
    }

    public GetProviderAvailabilityForDateQueryHandler(ApplicationDbContext db)
    {
        _db = db;
    }

    public async Task<Result<AvailabilityResponse>> Handle(
        GetProviderAvailabilityForDateQuery request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var date = request.Date;
            var dayOfWeek = date.DayOfWeek.ToString();

            // Combined query: leave, availability, buffer, booking windows
            var result = await (
                from provider in _db.Set<CareProviderProfile>()
                where provider.Id == request.ProviderId
                select new
                {
                    BufferDuration = provider.BufferDuration,

                    Availability = _db.Set<Availability>()
                        .Where(a => a.ProviderId == provider.Id && a.DayOfWeek == dayOfWeek)
                        .Select(a => new
                        {
                            a.IsAvailable,
                            Slots = a.AvailabilitySlots.Select(s => new { s.StartTime, s.EndTime }),
                        })
                        .FirstOrDefault(),

                    Bookings = _db.Set<BookingWindow>()
                        .Where(b => b.Booking.ProviderId == provider.Id && b.Date == date)
                        .Select(b => new { b.StartTime, b.EndTime })
                        .ToList(),

                    Leaves = _db.Set<Leave>()
                        .Where(l =>
                            l.ProviderId == provider.Id
                            && date >= DateOnly.FromDateTime(l.StartDate)
                            && date <= DateOnly.FromDateTime(l.EndDate)
                        )
                        .Select(l => new { l.StartDate, l.EndDate })
                        .ToList(),
                }
            ).FirstOrDefaultAsync(cancellationToken);

            if (result == null || result.Availability is null || !result.Availability.IsAvailable)
                return new AvailabilityResponse();

            var availableRanges = result
                .Availability.Slots.Select(s => new TimeRange(s.StartTime, s.EndTime))
                .ToList();

            var bookingsWithBuffer = result
                .Bookings.Select(b => new TimeRange(
                    b.StartTime.AddMinutes(-result.BufferDuration),
                    b.EndTime.AddMinutes(result.BufferDuration)
                ))
                .ToList();

            // Convert leaves to TimeRange for the specific date
            var leaveRanges = new List<TimeRange>();
            foreach (var leave in result.Leaves)
            {
                var leaveStartDate = DateOnly.FromDateTime(leave.StartDate);
                var leaveEndDate = DateOnly.FromDateTime(leave.EndDate);

                var startTime =
                    leaveStartDate < date
                        ? TimeOnly.MinValue
                        : TimeOnly.FromDateTime(leave.StartDate);
                var endTime =
                    leaveEndDate > date ? TimeOnly.MaxValue : TimeOnly.FromDateTime(leave.EndDate);

                // Only create TimeRange if it's valid (end > start)
                if (endTime > startTime)
                {
                    leaveRanges.Add(new TimeRange(startTime, endTime));
                }
            }

            // Combine bookings and leaves for subtraction
            var allBlockages = bookingsWithBuffer.Concat(leaveRanges).ToList();

            var freeRanges = TimeRange.SubtractMultiple(availableRanges, allBlockages);

            var response = new AvailabilityResponse
            {
                Id = request.ProviderId,
                Day = dayOfWeek,
                Available = freeRanges.Any(),
                Slots = freeRanges
                    .Select(slot => new TimeSlotResponse
                    {
                        StartTime = slot.Start.ToString("HH:mm"),
                        EndTime = slot.End.ToString("HH:mm"),
                    })
                    .ToList(),
            };

            _logger.LogInformation("Availability retrieved successfully");

            return Result<AvailabilityResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occured while retiving availability {ex.Message}");
            return Result.Failure<AvailabilityResponse>(
                Error.Internal(
                    $"An internal error occured while retiving availability {ex.Message}"
                )
            );
        }
    }
}
