using SuperCareApp.Application.Common.Interfaces;

namespace SuperCareApp.Persistence.Test;

public class TestCurrentUserService : ICurrentUserService
{
    public Guid? UserId => Guid.NewGuid();

    private string RoleName { get; set; } = "Admin";

    public bool IsAuthenticated => true;

    public void Clear()
    {
        return;
    }

    public bool IsInRole(string roleName)
    {
        RoleName = roleName;
        return true;
    }
}
