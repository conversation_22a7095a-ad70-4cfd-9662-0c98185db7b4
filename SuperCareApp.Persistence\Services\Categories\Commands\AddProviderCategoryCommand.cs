﻿using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Categories;
using SuperCareApp.Domain.Entities;

namespace SuperCareApp.Persistence.Services.Categories.Commands
{
    /// <summary>
    /// Command to add a care category to a provider
    /// </summary>
    public record AddProviderCategoryCommand(
        Guid ProviderId,
        ProviderCategoryRequest Request,
        Guid UserId
    ) : ICommand<Result>;

    /// <summary>
    /// Handler for the AddProviderCategoryCommand
    /// </summary>
    internal sealed class AddProviderCategoryCommandHandler
        : ICommandHandler<AddProviderCategoryCommand, Result>
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<AddProviderCategoryCommandHandler> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        public AddProviderCategoryCommandHandler(
            ApplicationDbContext dbContext,
            ILogger<AddProviderCategoryCommandHandler> logger
        )
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        /// <summary>
        /// Handles the command
        /// </summary>
        public async Task<Result> Handle(
            AddProviderCategoryCommand request,
            CancellationToken cancellationToken
        )
        {
            try
            {
                // Verify the provider exists
                var provider = await _dbContext
                    .Set<CareProviderProfile>()
                    .FirstOrDefaultAsync(
                        p => p.UserId == request.ProviderId && !p.IsDeleted,
                        cancellationToken
                    );

                if (provider == null)
                {
                    return Result.Failure(
                        Error.NotFound($"Care provider with ID {request.ProviderId} not found.")
                    );
                }

                // Verify the category exists and is active
                var category = await _dbContext
                    .Set<CareCategory>()
                    .FirstOrDefaultAsync(
                        c => c.Id == request.Request.CategoryId && !c.IsDeleted,
                        cancellationToken
                    );

                if (category == null)
                {
                    return Result.Failure(
                        Error.NotFound(
                            $"Care category with ID {request.Request.CategoryId} not found."
                        )
                    );
                }

                if (!category.IsActive)
                {
                    return Result.Failure(
                        Error.Validation(
                            $"Care category with ID {request.Request.CategoryId} is not active."
                        )
                    );
                }

                // Check if the provider already has this category
                var existingCategory = await _dbContext
                    .Set<CareProviderCategory>()
                    .FirstOrDefaultAsync(
                        pc =>
                            pc.ProviderId == provider.Id
                            && pc.CategoryId == request.Request.CategoryId
                            && !pc.IsDeleted,
                        cancellationToken
                    );

                if (existingCategory != null)
                {
                    return Result.Failure(
                        Error.Conflict(
                            $"Provider already has the category with ID {request.Request.CategoryId}."
                        )
                    );
                }

                // Add the category to the provider
                var providerCategory = new CareProviderCategory
                {
                    Id = Guid.NewGuid(),
                    ProviderId = provider.Id,
                    CategoryId = request.Request.CategoryId,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = request.UserId,
                };

                await _dbContext
                    .Set<CareProviderCategory>()
                    .AddAsync(providerCategory, cancellationToken);
                await _dbContext.SaveChangesAsync(cancellationToken);

                _logger.LogInformation(
                    "Added category {CategoryId} to provider {ProviderId}",
                    request.Request.CategoryId,
                    provider.Id
                );

                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding category to provider");
                return Result.Failure(Error.Internal(ex.Message));
            }
        }
    }
}
