# Recommendation Engine Integration Plan

## Overview

This document outlines the plan to integrate the existing `RTreeRecommendationEngine` into the Account controller's `GetProfiles` endpoint when fetching care providers. The goal is to enhance the care provider search functionality with intelligent recommendations based on location, services, and availability.

## Current State Analysis

### Existing Components
1. **RTreeRecommendationEngine**: Implemented spatial indexing using R-Tree for efficient location-based queries
2. **IRecommendationEngine**: Interface defining recommendation contracts
3. **AccountController.GetProfiles**: Endpoint that fetches care providers with filtering and pagination
4. **GetProfilesQuery**: Query handler that processes care provider searches with various filters

### Current Limitations
- No intelligent ranking based on proximity and relevance
- Basic filtering without recommendation scoring
- No integration of the recommendation engine with existing search functionality

## Implementation Plan

### Phase 1: Infrastructure Setup

#### 1.1 Register Recommendation Engine in DI Container
**File**: `SuperCareApp.Persistence/DependencyInjection.cs`

**Action**: Add the recommendation engine registration to the `AddApplicationServices` method:

```csharp
// Add to AddApplicationServices method
services.AddSingleton<IRecommendationEngine, RTreeRecommendationEngine>();
```

**Rationale**: Singleton registration ensures the spatial index is built once and reused across requests for optimal performance.

#### 1.2 Create Recommendation Service
**File**: `SuperCareApp.Application/Common/Interfaces/Bookings/IRecommendationService.cs`

**Purpose**: Create a service layer that orchestrates between the recommendation engine and the existing query system.

**Interface Definition**:
```csharp
public interface IRecommendationService
{
    Task InitializeEngineAsync(CancellationToken cancellationToken = default);
    Task<List<CareProviderProfile>> GetRecommendedProvidersAsync(
        double clientLat,
        double clientLon,
        string? requiredService,
        TimeOnly? requestedStart,
        TimeOnly? requestedEnd,
        double searchRadiusKm = 5,
        int topN = 10,
        CancellationToken cancellationToken = default);
    Task RefreshProviderDataAsync(CareProviderProfile provider);
    Task RemoveProviderAsync(Guid providerId);
}
```

#### 1.3 Implement Recommendation Service
**File**: `SuperCareApp.Persistence/Services/Bookings/RecommendationService.cs`

**Key Features**:
- Initialize the R-Tree with all verified care providers
- Handle real-time updates when provider data changes
- Integrate with existing filtering logic
- Provide fallback to standard search when recommendations are insufficient

### Phase 2: Query Enhancement

#### 2.1 Extend ProfileListParams
**File**: `SuperCareApp.Application/Common/Models/Identity/ProfileListParams.cs`

**Enhancement**: Add recommendation-specific parameters:

```csharp
public class ProfileListParams
{
    // Existing properties...
    
    // New recommendation properties
    public bool UseRecommendations { get; set; } = false;
    public double? ClientLatitude { get; set; }
    public double? ClientLongitude { get; set; }
    public TimeOnly? PreferredStartTime { get; set; }
    public TimeOnly? PreferredEndTime { get; set; }
    public string? PreferredService { get; set; }
    public double SearchRadiusKm { get; set; } = 5.0;
}
```

#### 2.2 Update ProfileQueryParams
**File**: `super-care-app/Models/Identity/ProfileQueryParams.cs`

**Enhancement**: Add query parameters for recommendation features:

```csharp
public class ProfileQueryParams
{
    // Existing properties...
    
    // New recommendation query parameters
    [FromQuery(Name = "useRecommendations")]
    public bool UseRecommendations { get; set; } = false;
    
    [FromQuery(Name = "clientLat")]
    public double? ClientLatitude { get; set; }
    
    [FromQuery(Name = "clientLon")]
    public double? ClientLongitude { get; set; }
    
    [FromQuery(Name = "preferredStartTime")]
    public TimeOnly? PreferredStartTime { get; set; }
    
    [FromQuery(Name = "preferredEndTime")]
    public TimeOnly? PreferredEndTime { get; set; }
    
    [FromQuery(Name = "preferredService")]
    public string? PreferredService { get; set; }
    
    [FromQuery(Name = "searchRadius")]
    public double SearchRadiusKm { get; set; } = 5.0;
}
```

#### 2.3 Enhance GetProfilesQueryHandler
**File**: `SuperCareApp.Persistence/Services/Identity/Queries/GetProfilesQuery.cs`

**Modifications**:
1. Inject `IRecommendationService`
2. Add recommendation logic to `GetCareProviderProfilesAsync`
3. Implement hybrid approach: recommendations + traditional filtering
4. Maintain backward compatibility

**Key Logic**:
```csharp
private async Task<Result<PagedProfileList>> GetCareProviderProfilesAsync(
    ProfileListParams parameters,
    string? searchTerm,
    CancellationToken cancellationToken)
{
    // If recommendations are requested and location is provided
    if (parameters.UseRecommendations && 
        parameters.ClientLatitude.HasValue && 
        parameters.ClientLongitude.HasValue)
    {
        return await GetRecommendedProvidersAsync(parameters, searchTerm, cancellationToken);
    }
    
    // Fall back to existing logic
    return await GetStandardProvidersAsync(parameters, searchTerm, cancellationToken);
}
```

### Phase 3: Controller Integration

#### 3.1 Update AccountController.GetProfiles
**File**: `super-care-app/Controllers/AccountController.cs`

**Enhancements**:
1. Map new query parameters to ProfileListParams
2. Add validation for recommendation parameters
3. Update API documentation

**Key Changes**:
```csharp
// Map to ProfileListParams with recommendation parameters
var paginationParams = new ProfileListParams
{
    // Existing mappings...
    
    // New recommendation mappings
    UseRecommendations = queryParams.UseRecommendations,
    ClientLatitude = queryParams.ClientLatitude,
    ClientLongitude = queryParams.ClientLongitude,
    PreferredStartTime = queryParams.PreferredStartTime,
    PreferredEndTime = queryParams.PreferredEndTime,
    PreferredService = queryParams.PreferredService,
    SearchRadiusKm = queryParams.SearchRadiusKm
};
```

#### 3.2 Add Validation
**File**: `SuperCareApp.Application/Common/Validators/ProfileQueryParamsValidator.cs`

**Validation Rules**:
- If `UseRecommendations` is true, `ClientLatitude` and `ClientLongitude` must be provided
- Latitude must be between -90 and 90
- Longitude must be between -180 and 180
- SearchRadiusKm must be positive and reasonable (e.g., max 50km)

### Phase 4: Background Services

#### 4.1 Create Recommendation Engine Initializer
**File**: `SuperCareApp.Persistence/Services/Bookings/RecommendationEngineInitializer.cs`

**Purpose**: Background service to initialize and maintain the recommendation engine

**Features**:
- Initialize engine on application startup
- Periodic refresh of provider data
- Handle provider updates in real-time

#### 4.2 Register Background Service
**File**: `SuperCareApp.Persistence/DependencyInjection.cs`

```csharp
services.AddHostedService<RecommendationEngineInitializer>();
```

### Phase 5: Response Enhancement

#### 5.1 Extend ProfileResponse
**File**: `SuperCareApp.Application/Common/Models/Identity/ProfileResponse.cs`

**Enhancement**: Add recommendation-specific metadata:

```csharp
public class ProfileResponse
{
    // Existing properties...
    
    // Recommendation metadata
    public double? DistanceKm { get; set; }
    public double? RecommendationScore { get; set; }
    public string? RecommendationReason { get; set; }
    public bool IsRecommended { get; set; }
}
```

#### 5.2 Update Response Mapping
**File**: `SuperCareApp.Persistence/Services/Identity/Queries/GetProfilesQuery.cs`

**Enhancement**: Include recommendation metadata in response mapping when recommendations are used.

## Implementation Sequence

### Week 1: Foundation
1. Register recommendation engine in DI container
2. Create IRecommendationService interface
3. Implement basic RecommendationService
4. Add unit tests for recommendation service

### Week 2: Query Integration
1. Extend ProfileListParams and ProfileQueryParams
2. Update GetProfilesQueryHandler with recommendation logic
3. Implement hybrid search approach
4. Add integration tests

### Week 3: Controller Enhancement
1. Update AccountController.GetProfiles
2. Add parameter validation
3. Update API documentation
4. Test end-to-end functionality

### Week 4: Background Services & Optimization
1. Implement RecommendationEngineInitializer
2. Add real-time provider updates
3. Performance testing and optimization
4. Documentation and deployment

## API Usage Examples

### Basic Recommendation Request
```http
GET /api/v1/account/profiles?usertype=CareProvider&useRecommendations=true&clientLat=40.7128&clientLon=-74.0060&preferredService=Nursing&preferredStartTime=09:00&preferredEndTime=17:00
```

### Recommendation with Filters
```http
GET /api/v1/account/profiles?usertype=CareProvider&useRecommendations=true&clientLat=40.7128&clientLon=-74.0060&searchRadius=10&minExperience=2&maxPrice=50&categoryIds=1,2,3
```

### Fallback to Standard Search
```http
GET /api/v1/account/profiles?usertype=CareProvider&useRecommendations=false&locationLat=40.7128&locationLong=-74.0060&distanceRadius=10
```

## Performance Considerations

### Optimization Strategies
1. **Singleton R-Tree**: Maintain single instance for optimal memory usage
2. **Lazy Loading**: Initialize engine only when first recommendation request is made
3. **Caching**: Cache recommendation results for frequently requested locations
4. **Batch Updates**: Update R-Tree in batches rather than individual provider updates
5. **Async Processing**: Use background services for heavy initialization

### Monitoring Metrics
1. Recommendation engine initialization time
2. Average recommendation response time
3. Cache hit/miss ratios
4. R-Tree memory usage
5. Provider update frequency

## Testing Strategy

### Unit Tests
1. RecommendationService logic
2. R-Tree spatial queries
3. Parameter validation
4. Response mapping

### Integration Tests
1. End-to-end recommendation flow
2. Fallback scenarios
3. Performance under load
4. Data consistency

### Performance Tests
1. Large dataset handling (10k+ providers)
2. Concurrent request handling
3. Memory usage patterns
4. Response time benchmarks

## Risk Mitigation

### Potential Issues & Solutions

1. **Memory Usage**: R-Tree with large datasets
   - **Solution**: Implement data pagination and selective loading

2. **Initialization Time**: Loading all providers on startup
   - **Solution**: Lazy initialization and background loading

3. **Data Staleness**: R-Tree not reflecting latest provider data
   - **Solution**: Real-time update mechanisms and periodic refresh

4. **Performance Degradation**: Complex spatial queries
   - **Solution**: Query optimization and result caching

5. **Backward Compatibility**: Breaking existing API consumers
   - **Solution**: Maintain existing behavior as default, recommendations opt-in

## Success Metrics

### Technical Metrics
- Recommendation response time < 200ms for 95% of requests
- Memory usage increase < 100MB for 10k providers
- 99.9% uptime for recommendation service

### Business Metrics
- Improved user engagement with recommended providers
- Reduced search time for clients
- Higher booking conversion rates
- Better geographic distribution of bookings

## Future Enhancements

### Phase 2 Features
1. **Machine Learning Integration**: Incorporate user behavior and booking history
2. **Real-time Availability**: Integration with provider calendar systems
3. **Advanced Scoring**: Multi-factor recommendation scoring (rating, reviews, specializations)
4. **Personalization**: User preference learning and adaptation
5. **A/B Testing**: Framework for testing different recommendation algorithms

### Scalability Improvements
1. **Distributed R-Tree**: For multi-region deployments
2. **Redis Caching**: For high-frequency recommendation caching
3. **Event-Driven Updates**: Real-time provider data synchronization
4. **Analytics Integration**: Recommendation effectiveness tracking

## Conclusion

This implementation plan provides a comprehensive approach to integrating the recommendation engine into the existing care provider search functionality. The phased approach ensures minimal disruption to existing functionality while adding powerful recommendation capabilities that will enhance user experience and improve matching between clients and care providers.

The plan emphasizes backward compatibility, performance optimization, and thorough testing to ensure a robust and scalable solution.