﻿using FluentValidation;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using SuperCareApp.Application.Common.Interfaces.Address;
using SuperCareApp.Application.Common.Interfaces.Bookings;
using SuperCareApp.Application.Common.Interfaces.Calendar;
using SuperCareApp.Application.Common.Interfaces.Categories;
using SuperCareApp.Application.Common.Interfaces.Repositories;
using SuperCareApp.Application.Shared.Dispatcher;
using SuperCareApp.Persistence.Interceptors;
using SuperCareApp.Persistence.Services.Address;
using SuperCareApp.Persistence.Services.Bookings;
using SuperCareApp.Persistence.Services.Calendar;
using SuperCareApp.Persistence.Services.Categories;
using SuperCareApp.Persistence.Services.Shared.Dispatcher;

namespace SuperCareApp.Persistence;

public static class DependencyInjection
{
    /// <summary>
    /// An orchestrator method to configure all services for the persistence and infrastructure layers.
    /// </summary>
    public static IServiceCollection AddPersistence(
        this IServiceCollection services,
        IConfiguration configuration
    )
    {
        services
            .AddDatabase(configuration)
            .AddIdentity(configuration)
            .AddApplicationServices()
            .AddInfrastructureServices(configuration)
            .AddFrameworkComponents(configuration);

        return services;
    }

    /// <summary>
    /// Configures the database context, Unit of Work, and repositories.
    /// </summary>
    private static IServiceCollection AddDatabase(
        this IServiceCollection services,
        IConfiguration configuration
    )
    {
        // Register audit services
        services.AddScoped<IAuditConfiguration, AuditConfiguration>();
        services.AddScoped<AuditInterceptor>();
        services.AddScoped<DbPersistanceInterceptor>();

        // Register database context
        services.AddDbContext<ApplicationDbContext>(
            (serviceProvider, options) =>
            {
                var auditInterceptor = serviceProvider.GetRequiredService<AuditInterceptor>();
                var dbPersistanceInterceptor =
                    serviceProvider.GetRequiredService<DbPersistanceInterceptor>();
                var currentUserService = serviceProvider.GetRequiredService<ICurrentUserService>();

                options
                    .UseNpgsql(
                        configuration.GetConnectionString("DefaultConnection"),
                        b =>
                        {
                            b.MigrationsHistoryTable("__ef_migrations_history");
                            b.MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName);
                        }
                    )
                    .UseSnakeCaseNamingConvention()
                    .AddInterceptors(auditInterceptor, dbPersistanceInterceptor);
            }
        );

        // Register Unit of Work and all Repositories
        services.AddScoped<IUnitOfWork, UnitOfWork.UnitOfWork>();
        services.AddScoped<IOtpCodeRepository, OtpCodeRepository>();
        services.AddScoped<IUserProfileRepository, UserProfileRepository>();
        services.AddScoped<ICareCategoryRepository, CareCategoryRepository>();

        // Register generic repositories
        services.AddScoped(typeof(IRepository<>), typeof(GenericRepository<>));

        return services;
    }

    /// <summary>
    /// Configures ASP.NET Core Identity, JWT authentication, and authorization.
    /// </summary>
    private static IServiceCollection AddIdentity(
        this IServiceCollection services,
        IConfiguration configuration
    )
    {
        services
            .AddIdentity<ApplicationUser, ApplicationRole>(options =>
            {
                options.Password.RequireDigit = true;
                options.Password.RequireLowercase = true;
                options.Password.RequireUppercase = true;
                options.Password.RequireNonAlphanumeric = true;
                options.Password.RequiredLength = 8;
                options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(15);
                options.Lockout.MaxFailedAccessAttempts = 5;
                options.User.RequireUniqueEmail = true;
            })
            .AddSignInManager<SignInManager<ApplicationUser>>()
            .AddEntityFrameworkStores<ApplicationDbContext>()
            .AddDefaultTokenProviders();

        // Configure JWT settings & Authentication
        var jwtSettingsSection = configuration.GetSection("JwtSettings");
        services.Configure<JwtSettings>(jwtSettingsSection);
        var jwtSettings = jwtSettingsSection.Get<JwtSettings>();
        var key = Encoding.ASCII.GetBytes(
            jwtSettings?.Secret ?? "DefaultSecretKeyForDevelopmentOnly12345678901234567890"
        );

        services
            .AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = false;
                options.SaveToken = true;
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = jwtSettings?.Issuer ?? "SuperCareApp",
                    ValidateAudience = true,
                    ValidAudience = jwtSettings?.Audience ?? "SuperCareAppUsers",
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero,
                };
                // Note: The detailed JwtBearerEvents can be further abstracted if needed.
            });

        services.AddAuthorization(options => { });

        // Register core identity-related services
        services.AddScoped<IAuthService, AuthService>();
        services.AddScoped<ITokenService, TokenService>();
        services.AddScoped<ICurrentUserService, CurrentUserService>();
        services.AddSingleton<IDateTimeProvider, SystemDateTimeProvider>();
        return services;
    }

    /// <summary>
    /// Registers the application's business logic services.
    /// </summary>
    private static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IUserProfileService, UserProfileService>();
        services.AddScoped<IAddressService, AddressService>();
        services.AddScoped<IBookingManagementService, BookingManagementService>();
        services.AddScoped<IProviderAvailabilityReadModel, ProviderAvailabilityReadModel>();
        services.AddScoped<IProviderScheduleService, ProviderScheduleService>();
        services.AddScoped<IBookingService, BookingService>();
        services.AddScoped<IAvailabilityService, AvailabilityService>();
        services.AddScoped<ICalendarService, CalendarService>();
        services.AddScoped<ICareCategoryService, CareCategoryService>();
        services.AddScoped<ICareProviderService, CareProviderService>();
        services.AddScoped<ICareProviderProfileService, CareProviderProfileService>();
        services.AddScoped<IApprovalService, ApprovalService>();
        services.AddScoped<IAuditService, AuditService>();
        services.AddScoped<IDashboardStatisticsService, DashboardStatisticsService>();
        services.AddSingleton<IRecommendationEngine, RTreeRecommendationEngine>();
        services.AddScoped<IRecommendationService, RecommendationService>();
        services.AddSingleton<IPaymentStore, InMemoryPaymentStore>();
        services.AddScoped<IAvailabilityTemplateService, AvailabilityTemplateService>();
        return services;
    }

    /// <summary>
    /// Registers services that interact with external systems (Email, SMS, etc.).
    /// </summary>
    private static IServiceCollection AddInfrastructureServices(
        this IServiceCollection services,
        IConfiguration configuration
    )
    {
        services.AddTwilio(configuration);
        services.AddDocumentServices();

        services.Configure<SmtpSettings>(configuration.GetSection("Smtp"));
        services.AddTransient<IMailSender>(provider =>
        {
            var settings = provider.GetRequiredService<IOptions<SmtpSettings>>().Value;
            var logger = provider.GetRequiredService<ILogger<MailSender>>();
            return new MailSender(
                settings.Host,
                settings.Port,
                settings.FromEmail,
                settings.Password,
                settings.FromEmail,
                settings.EnableSsl,
                logger
            );
        });
        services.AddSingleton<InvoicePdfBuilder>();
        return services;
    }

    /// <summary>
    /// Registers cross-cutting concerns and framework-level components like MediatR and Caching.
    /// </summary>
    private static IServiceCollection AddFrameworkComponents(
        this IServiceCollection services,
        IConfiguration configuration
    )
    {
        services.AddHttpContextAccessor();
        services.AddRazorTemplating();
        services.AddMemoryCache(); // Simplified for clarity; original logic is fine.

        // Register MediatR, Behaviors, and Validators
        services.AddMediator(typeof(DependencyInjection).Assembly);
        services.AddPipelineBehaviors();
        services.AddValidatorsFromAssembly(typeof(DependencyInjection).Assembly);

        // Register Dispatchers
        services.AddScoped<IOtpDispatcher, OtpDispatcher>();

        return services;
    }
}
