namespace SuperCareApp.Application.Common.Models.Bookings;

public class UpdateBookingResponse
{
    public Guid BookingId { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public decimal PlatformFee { get; set; }
    public decimal ProviderAmount { get; set; }
    public List<BookingWindowResponse> BookingWindows { get; set; } = new();
    public DateTime UpdatedAt { get; set; }
    public string? SpecialInstructions { get; set; }
    public int TotalHours { get; set; }
}
