# Display ID Mapping Implementation Plan
## Super Care App - Booking and User Display IDs

### 1. Overview and Requirements

#### Business Context
- **Problem**: Internal GUIDs are not user-friendly for customer service, support tickets, and user communication
- **Solution**: Implement sequential, human-readable display IDs while maintaining GUID-based internal architecture
- **Scope**: Booking and User entities initially, with extensibility for other entities

#### Key Requirements
- **User-Friendly**: Sequential numeric IDs (e.g., U-000001, B-000001)
- **Unique**: No collisions across the system
- **Scalable**: Handle high-volume concurrent operations
- **Backwards Compatible**: Existing GUID-based relationships remain intact
- **Secure**: Display IDs don't expose business intelligence
- **Reversible**: Easy mapping between display ID and GUID

### 2. Technical Architecture

#### 2.1 Database Schema Design

**New Entity: DisplayIdMapping**
```sql
CREATE TABLE DisplayIdMapping (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    EntityType NVARCHAR(50) NOT NULL,
    EntityId UNIQUEIDENTIFIER NOT NULL,
    DisplayId NVARCHAR(20) NOT NULL,
    SequenceNumber BIGINT NOT NULL,
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    CONSTRAINT UK_DisplayIdMapping_EntityType_EntityId UNIQUE (EntityType, EntityId),
    CONSTRAINT UK_DisplayIdMapping_DisplayId UNIQUE (DisplayId),
    CONSTRAINT UK_DisplayIdMapping_EntityType_SequenceNumber UNIQUE (EntityType, SequenceNumber)
);

CREATE INDEX IX_DisplayIdMapping_EntityType_SequenceNumber 
    ON DisplayIdMapping (EntityType, SequenceNumber);
CREATE INDEX IX_DisplayIdMapping_DisplayId 
    ON DisplayIdMapping (DisplayId);
```

**Sequence Generator Table**
```sql
CREATE TABLE DisplayIdSequence (
    EntityType NVARCHAR(50) PRIMARY KEY,
    CurrentValue BIGINT NOT NULL DEFAULT 0,
    LastUpdated DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);

-- Initialize sequences
INSERT INTO DisplayIdSequence (EntityType, CurrentValue) VALUES 
    ('User', 0),
    ('Booking', 0);
```

#### 2.2 Entity Model Updates

**DisplayIdMapping Entity**
```csharp
public class DisplayIdMapping : BaseEntity
{
    public string EntityType { get; set; } = string.Empty;
    public Guid EntityId { get; set; }
    public string DisplayId { get; set; } = string.Empty;
    public long SequenceNumber { get; set; }
}
```

**DisplayIdSequence Entity**
```csharp
public class DisplayIdSequence
{
    public string EntityType { get; set; } = string.Empty;
    public long CurrentValue { get; set; }
    public DateTime LastUpdated { get; set; }
}
```

**Updated User and Booking Entities**
```csharp
// Add to UserProfile
public string? DisplayId { get; set; }

// Add to Booking
public string? DisplayId { get; set; }
```

### 3. Implementation Strategy

#### 3.1 Display ID Generation Service

**IDisplayIdService Interface**
```csharp
public interface IDisplayIdService
{
    Task<string> GenerateDisplayIdAsync(string entityType, Guid entityId);
    Task<Guid?> GetEntityIdFromDisplayIdAsync(string displayId);
    Task<string?> GetDisplayIdFromEntityIdAsync(string entityType, Guid entityId);
    Task<bool> ValidateDisplayIdAsync(string displayId);
}
```

**Implementation Approach**
```csharp
public class DisplayIdService : IDisplayIdService
{
    private readonly IDbContext _context;
    private readonly ILogger<DisplayIdService> _logger;
    
    // Display ID Format: {PREFIX}-{SEQUENCE}
    private readonly Dictionary<string, string> _prefixMap = new()
    {
        { "User", "U" },
        { "Booking", "B" }
    };
    
    public async Task<string> GenerateDisplayIdAsync(string entityType, Guid entityId)
    {
        // Check if display ID already exists
        var existing = await _context.DisplayIdMappings
            .FirstOrDefaultAsync(x => x.EntityType == entityType && x.EntityId == entityId);
        
        if (existing != null)
            return existing.DisplayId;
        
        // Generate new display ID with transaction safety
        using var transaction = await _context.Database.BeginTransactionAsync();
        
        try
        {
            var sequenceNumber = await GetNextSequenceAsync(entityType);
            var displayId = FormatDisplayId(entityType, sequenceNumber);
            
            var mapping = new DisplayIdMapping
            {
                EntityType = entityType,
                EntityId = entityId,
                DisplayId = displayId,
                SequenceNumber = sequenceNumber
            };
            
            _context.DisplayIdMappings.Add(mapping);
            await _context.SaveChangesAsync();
            await transaction.CommitAsync();
            
            return displayId;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
    
    private async Task<long> GetNextSequenceAsync(string entityType)
    {
        // Use SQL Server's UPDLOCK to prevent race conditions
        var sequence = await _context.DisplayIdSequences
            .FromSqlRaw("SELECT * FROM DisplayIdSequence WITH (UPDLOCK) WHERE EntityType = {0}", entityType)
            .FirstOrDefaultAsync();
        
        if (sequence == null)
        {
            sequence = new DisplayIdSequence 
            { 
                EntityType = entityType, 
                CurrentValue = 1 
            };
            _context.DisplayIdSequences.Add(sequence);
        }
        else
        {
            sequence.CurrentValue++;
            sequence.LastUpdated = DateTime.UtcNow;
        }
        
        await _context.SaveChangesAsync();
        return sequence.CurrentValue;
    }
    
    private string FormatDisplayId(string entityType, long sequenceNumber)
    {
        var prefix = _prefixMap.GetValueOrDefault(entityType, "X");
        return $"{prefix}-{sequenceNumber:D6}"; // e.g., U-000001, B-000001
    }
}
```

#### 3.2 Integration Points

**Repository Pattern Updates**
```csharp
public abstract class BaseRepository<T> where T : BaseEntity
{
    protected async Task<T> CreateWithDisplayIdAsync(T entity, string entityType)
    {
        _context.Set<T>().Add(entity);
        await _context.SaveChangesAsync();
        
        // Generate display ID after entity creation
        var displayId = await _displayIdService.GenerateDisplayIdAsync(entityType, entity.Id);
        
        // Update entity with display ID if it has the property
        if (entity.GetType().GetProperty("DisplayId") != null)
        {
            entity.GetType().GetProperty("DisplayId")?.SetValue(entity, displayId);
            await _context.SaveChangesAsync();
        }
        
        return entity;
    }
}
```

**Service Layer Integration**
```csharp
// BookingService
public async Task<Booking> CreateBookingAsync(CreateBookingRequest request)
{
    var booking = new Booking
    {
        // ... set properties
    };
    
    return await _bookingRepository.CreateWithDisplayIdAsync(booking, "Booking");
}

// UserService
public async Task<UserProfile> CreateUserAsync(CreateUserRequest request)
{
    var user = new UserProfile
    {
        // ... set properties
    };
    
    return await _userRepository.CreateWithDisplayIdAsync(user, "User");
}
```

### 4. Migration Strategy

#### 4.1 Database Migration

**Step 1: Create New Tables**
```sql
-- Create DisplayIdMapping and DisplayIdSequence tables
-- Add DisplayId columns to existing entities
ALTER TABLE UserProfile ADD DisplayId NVARCHAR(20) NULL;
ALTER TABLE Booking ADD DisplayId NVARCHAR(20) NULL;
```

**Step 2: Backfill Existing Data**
```sql
-- Stored procedure to backfill display IDs
CREATE PROCEDURE BackfillDisplayIds
AS
BEGIN
    DECLARE @entityType NVARCHAR(50), @entityId UNIQUEIDENTIFIER, @counter BIGINT;
    
    -- Backfill Users
    SET @counter = 0;
    DECLARE user_cursor CURSOR FOR 
        SELECT Id FROM UserProfile ORDER BY CreatedAt;
    
    OPEN user_cursor;
    FETCH NEXT FROM user_cursor INTO @entityId;
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        SET @counter = @counter + 1;
        
        INSERT INTO DisplayIdMapping (EntityType, EntityId, DisplayId, SequenceNumber)
        VALUES ('User', @entityId, 'U-' + RIGHT('000000' + CAST(@counter AS VARCHAR), 6), @counter);
        
        UPDATE UserProfile 
        SET DisplayId = 'U-' + RIGHT('000000' + CAST(@counter AS VARCHAR), 6)
        WHERE Id = @entityId;
        
        FETCH NEXT FROM user_cursor INTO @entityId;
    END;
    
    CLOSE user_cursor;
    DEALLOCATE user_cursor;
    
    -- Update sequence
    UPDATE DisplayIdSequence SET CurrentValue = @counter WHERE EntityType = 'User';
    
    -- Repeat similar process for Bookings
END;
```

#### 4.2 Application Migration

**Phase 1: Database Schema (Week 1)**
- Deploy new tables and columns
- Run backfill scripts during maintenance window
- Validate data integrity

**Phase 2: Application Logic (Week 2)**
- Deploy display ID generation service
- Update create operations to generate display IDs
- Add display ID lookup methods

**Phase 3: API Updates (Week 3)**
- Update API responses to include display IDs
- Add endpoints for display ID lookups
- Update documentation

**Phase 4: Frontend Integration (Week 4)**
- Update UI to show display IDs
- Add search by display ID functionality
- Update customer service tools

### 5. API Design

#### 5.1 New Endpoints

**Display ID Lookup**
```http
GET /api/v1/display-ids/lookup/{displayId}
Response: {
    "entityType": "User",
    "entityId": "123e4567-e89b-12d3-a456-426614174000",
    "displayId": "U-000001"
}
```

**Entity Lookup by Display ID**
```http
GET /api/v1/users/by-display-id/{displayId}
GET /api/v1/bookings/by-display-id/{displayId}
```

#### 5.2 Response Format Updates

**Updated User Response**
```json
{
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "displayId": "U-000001",
    "firstName": "John",
    "lastName": "Doe",
    // ... other fields
}
```

**Updated Booking Response**
```json
{
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "displayId": "B-000001",
    "clientDisplayId": "U-000001",
    "providerDisplayId": "U-000002",
    // ... other fields
}
```

### 6. Performance Considerations

#### 6.1 Database Optimizations

**Indexing Strategy**
- Clustered index on DisplayId for fast lookups
- Composite index on (EntityType, SequenceNumber) for generation
- Consider partitioning for high-volume scenarios

**Caching Strategy**
```csharp
public class CachedDisplayIdService : IDisplayIdService
{
    private readonly IDisplayIdService _inner;
    private readonly IMemoryCache _cache;
    
    public async Task<string?> GetDisplayIdFromEntityIdAsync(string entityType, Guid entityId)
    {
        var cacheKey = $"displayid:{entityType}:{entityId}";
        
        if (_cache.TryGetValue(cacheKey, out string? cachedDisplayId))
            return cachedDisplayId;
        
        var displayId = await _inner.GetDisplayIdFromEntityIdAsync(entityType, entityId);
        
        if (displayId != null)
        {
            _cache.Set(cacheKey, displayId, TimeSpan.FromHours(24));
        }
        
        return displayId;
    }
}
```

#### 6.2 Concurrency Handling

**Optimistic Locking for Sequence Generation**
```csharp
private async Task<long> GetNextSequenceWithRetryAsync(string entityType, int maxRetries = 3)
{
    for (int attempt = 0; attempt < maxRetries; attempt++)
    {
        try
        {
            return await GetNextSequenceAsync(entityType);
        }
        catch (DbUpdateConcurrencyException) when (attempt < maxRetries - 1)
        {
            await Task.Delay(100 * (attempt + 1)); // Exponential backoff
        }
    }
    
    throw new InvalidOperationException("Failed to generate sequence after multiple attempts");
}
```

### 7. Testing Strategy

#### 7.1 Unit Tests

**Display ID Generation Tests**
```csharp
[Test]
public async Task GenerateDisplayIdAsync_NewUser_ReturnsSequentialId()
{
    // Test sequential generation
    var userId1 = Guid.NewGuid();
    var userId2 = Guid.NewGuid();
    
    var displayId1 = await _displayIdService.GenerateDisplayIdAsync("User", userId1);
    var displayId2 = await _displayIdService.GenerateDisplayIdAsync("User", userId2);
    
    Assert.AreEqual("U-000001", displayId1);
    Assert.AreEqual("U-000002", displayId2);
}
```

**Concurrency Tests**
```csharp
[Test]
public async Task GenerateDisplayIdAsync_ConcurrentRequests_ReturnsUniqueIds()
{
    var tasks = Enumerable.Range(1, 100)
        .Select(i => _displayIdService.GenerateDisplayIdAsync("User", Guid.NewGuid()))
        .ToArray();
    
    var results = await Task.WhenAll(tasks);
    
    Assert.AreEqual(100, results.Distinct().Count());
}
```

#### 7.2 Integration Tests

**End-to-End Workflow Tests**
- Create user/booking → Verify display ID generation
- Lookup by display ID → Verify correct entity retrieval
- API response validation → Ensure display IDs included

#### 7.3 Performance Tests

**Load Testing Scenarios**
- Concurrent display ID generation (1000 requests/second)
- Lookup performance under load
- Database lock contention testing

### 8. Monitoring and Alerting

#### 8.1 Metrics to Track

**Generation Metrics**
- Display ID generation rate
- Sequence number gaps
- Generation failures
- Cache hit rates

**Performance Metrics**
- Average generation time
- Database lock wait times
- API response times for display ID lookups

#### 8.2 Alerting Rules

**Critical Alerts**
- Display ID generation failures > 1%
- Sequence number conflicts detected
- Database deadlocks on sequence table

**Warning Alerts**
- Cache miss rate > 20%
- Generation time > 500ms
- Sequence table lock contention

### 9. Documentation and Training

#### 9.1 Developer Documentation

**API Documentation**
- New endpoint specifications
- Response format changes
- Error handling guidelines

**Integration Guide**
- How to use display IDs in new features
- Migration checklist for existing code
- Best practices for display ID usage

#### 9.2 Customer Service Training

**Support Tool Updates**
- How to search by display ID
- Understanding display ID format
- Troubleshooting display ID issues

**Customer Communication**
- When to use display IDs with customers
- How to explain display ID benefits
- Handling display ID-related inquiries

### 10. Rollback Strategy

#### 10.1 Immediate Rollback

**Database Rollback**
```sql
-- Remove display ID columns (if safe)
ALTER TABLE UserProfile DROP COLUMN DisplayId;
ALTER TABLE Booking DROP COLUMN DisplayId;

-- Drop new tables
DROP TABLE DisplayIdMapping;
DROP TABLE DisplayIdSequence;
```

**Application Rollback**
- Deploy previous application version
- Disable display ID generation
- Revert API response formats

#### 10.2 Gradual Rollback

**Feature Flags**
```csharp
public class DisplayIdFeatureFlags
{
    public bool EnableDisplayIdGeneration { get; set; } = true;
    public bool ShowDisplayIdInResponses { get; set; } = true;
    public bool EnableDisplayIdLookup { get; set; } = true;
}
```

### 11. Future Considerations

#### 11.1 Extensibility

**Additional Entities**
- CareProviderProfile
- Payment
- Review
- Conversation

**Custom Format Support**
```csharp
public class DisplayIdFormat
{
    public string EntityType { get; set; }
    public string Prefix { get; set; }
    public int PadLength { get; set; }
    public string Separator { get; set; }
    public bool IncludeChecksum { get; set; }
}
```

#### 11.2 Advanced Features

**Hierarchical Display IDs**
- Parent-child relationships (e.g., B-000001-01 for booking amendments)
- Department-based prefixes (e.g., NYC-U-000001)

**Display ID Analytics**
- Usage patterns
- Customer service impact metrics
- Performance optimization insights

### 12. Success Criteria

#### 12.1 Technical Success

- ✅ 100% unique display ID generation
- ✅ Sub-100ms display ID generation time
- ✅ 99.9% uptime for display ID services
- ✅ Zero data integrity issues

#### 12.2 Business Success

- ✅ Improved customer service efficiency
- ✅ Reduced support ticket resolution time
- ✅ Enhanced user experience
- ✅ Simplified communication with customers

#### 12.3 Operational Success

- ✅ Seamless migration with zero downtime
- ✅ Successful team training completion
- ✅ Monitoring and alerting in place
- ✅ Documentation complete and accessible

---

## Implementation Timeline

| Phase | Duration | Key Activities |
|-------|----------|----------------|
| **Phase 1: Design & Planning** | 1 week | Finalize architecture, create detailed specifications |
| **Phase 2: Database Schema** | 1 week | Create tables, run migrations, backfill data |
| **Phase 3: Core Service** | 2 weeks | Implement DisplayIdService, add repository support |
| **Phase 4: API Integration** | 1 week | Update endpoints, modify responses |
| **Phase 5: Frontend Updates** | 2 weeks | Update UI, add search functionality |
| **Phase 6: Testing & QA** | 1 week | Comprehensive testing, performance validation |
| **Phase 7: Documentation** | 1 week | Complete documentation, training materials |
| **Phase 8: Deployment** | 1 week | Production deployment, monitoring setup |

**Total Timeline: 10 weeks**

This plan provides a comprehensive approach to implementing display ID mapping while maintaining system integrity and providing a smooth user experience.