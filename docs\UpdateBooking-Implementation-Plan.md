# UpdateBooking Implementation Plan

## Overview
This document outlines the comprehensive implementation plan for updating the UpdateBooking functionality to support multiple booking windows, proper conflict detection, and enhanced validation.

## Current State Analysis

### Issues Identified
1. **Single Window Limitation**: Current UpdateBookingRequest only supports single date/time, not multiple windows
2. **Incomplete Conflict Detection**: Missing proper overlap detection with existing bookings
3. **No Availability Validation**: Not checking if requested times fall within provider's available slots
4. **Financial Calculation Issues**: Manual amount calculation instead of automatic computation
5. **Missing Status Validation**: No check if booking can be updated based on current status

### Current Implementation Problems
- UpdateBookingRequest uses single DateTime + TimeOnly fields
- UpdateBookingCommand passes individual parameters instead of structured data
- BookingService.UpdateBookingAsync has basic validation but lacks comprehensive conflict checking
- No integration with the improved CreateBookingWithWindowsCommand logic

## Implementation Phases

### Phase 1: Update Request/Response Models
### Phase 2: Enhance Command Structure  
### Phase 3: Implement Service Layer Logic
### Phase 4: Add Comprehensive Validation
### Phase 5: Testing and Integration

## Detailed Implementation Plan

### Phase 1: Update Request/Response Models

#### 1.1 Create UpdateBookingWithWindowsRequest
```csharp
public class UpdateBookingWithWindowsRequest
{
    public Guid ProviderId { get; set; }
    public Guid CategoryId { get; set; }
    public string? SpecialInstructions { get; set; }
    public List<BookingWindowRequest> BookingWindows { get; set; } = new();
}

public class UpdateBookingWithWindowsRequestValidator : AbstractValidator<UpdateBookingWithWindowsRequest>
{
    public UpdateBookingWithWindowsRequestValidator()
    {
        RuleFor(x => x.ProviderId).NotEmpty().WithMessage("Provider ID is required.");
        RuleFor(x => x.CategoryId).NotEmpty().WithMessage("Category ID is required.");
        
        RuleFor(x => x.BookingWindows)
            .NotEmpty().WithMessage("At least one booking window is required.")
            .Must(HaveNoOverlappingWindows)
            .WithMessage("Booking windows must not overlap on the same date.");
            
        RuleForEach(x => x.BookingWindows).SetValidator(new BookingWindowRequestValidator());
        
        RuleFor(x => x.SpecialInstructions)
            .MaximumLength(500)
            .WithMessage("Special instructions cannot exceed 500 characters.");
    }
    
    private bool HaveNoOverlappingWindows(List<BookingWindowRequest> windows)
    {
        // Implementation similar to CreateBookingRequest validator
    }
}
```

#### 1.2 Create UpdateBookingResponse
```csharp
public class UpdateBookingResponse
{
    public Guid BookingId { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public decimal PlatformFee { get; set; }
    public decimal ProviderAmount { get; set; }
    public List<BookingWindowResponse> BookingWindows { get; set; } = new();
    public DateTime UpdatedAt { get; set; }
}
```

### Phase 2: Enhance Command Structure

#### 2.1 Create UpdateBookingWithWindowsCommand
```csharp
public record UpdateBookingWithWindowsCommand(
    Guid UserId, 
    Guid BookingId, 
    UpdateBookingWithWindowsRequest Request
) : ICommand<Result<UpdateBookingResponse>>;

internal sealed class UpdateBookingWithWindowsCommandHandler 
    : ICommandHandler<UpdateBookingWithWindowsCommand, Result<UpdateBookingResponse>>
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<UpdateBookingWithWindowsCommandHandler> _logger;

    public async Task<Result<UpdateBookingResponse>> Handle(
        UpdateBookingWithWindowsCommand command,
        CancellationToken cancellationToken)
    {
        // Implementation details in Phase 3
    }
}
```

### Phase 3: Implement Service Layer Logic

#### 3.1 Add UpdateBookingWithWindowsAsync to IBookingService
```csharp
Task<Result<UpdateBookingResponse>> UpdateBookingWithWindowsAsync(
    Guid userId,
    Guid bookingId,
    UpdateBookingWithWindowsRequest request,
    CancellationToken cancellationToken = default
);
```

#### 3.2 Implement Service Method
```csharp
public async Task<Result<UpdateBookingResponse>> UpdateBookingWithWindowsAsync(
    Guid userId,
    Guid bookingId,
    UpdateBookingWithWindowsRequest request,
    CancellationToken cancellationToken = default)
{
    try
    {
        // 1. Load existing booking with all related data
        var booking = await LoadBookingWithRelatedDataAsync(bookingId, cancellationToken);
        if (booking == null)
            return Result.Failure<UpdateBookingResponse>(Error.NotFound("Booking not found"));

        // 2. Validate user authorization
        if (!IsUserAuthorizedForBooking(userId, booking.ClientId, booking.ProviderId))
            return Result.Failure<UpdateBookingResponse>(Error.Unauthorized("Not authorized"));

        // 3. Validate booking status (can only update certain statuses)
        var statusValidation = ValidateBookingStatusForUpdate(booking.Status?.Status);
        if (!statusValidation.IsSuccess)
            return Result.Failure<UpdateBookingResponse>(statusValidation.Error);

        // 4. Validate entities exist
        var entitiesValidation = await ValidateUpdateEntitiesAsync(request, cancellationToken);
        if (!entitiesValidation.IsSuccess)
            return Result.Failure<UpdateBookingResponse>(entitiesValidation.Error);

        var (provider, category) = entitiesValidation.Value;

        // 5. Validate intra-request window overlaps
        var windowValidation = ValidateBookingWindows(request.BookingWindows);
        if (!windowValidation.IsSuccess)
            return Result.Failure<UpdateBookingResponse>(windowValidation.Error);

        // 6. Validate provider availability for new windows
        var availabilityValidation = await ValidateProviderAvailabilityAsync(
            provider, request.BookingWindows, cancellationToken);
        if (!availabilityValidation.IsSuccess)
            return Result.Failure<UpdateBookingResponse>(availabilityValidation.Error);

        // 7. Check conflicts with other bookings (excluding current booking)
        var conflictValidation = await ValidateBookingConflictsAsync(
            request.ProviderId, request.BookingWindows, bookingId, cancellationToken);
        if (!conflictValidation.IsSuccess)
            return Result.Failure<UpdateBookingResponse>(conflictValidation.Error);

        // 8. Calculate new amounts
        var amounts = CalculateBookingAmounts(provider, category, request.BookingWindows);

        // 9. Update booking in transaction
        using var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
        try
        {
            await UpdateBookingEntityAsync(booking, request, amounts, userId, cancellationToken);
            await CreateBookingStatusHistoryAsync(booking.Id, userId, "Booking updated", cancellationToken);
            
            await _context.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            // 10. Return response
            var response = CreateUpdateBookingResponse(booking, amounts);
            return Result.Success(response);
        }
        catch
        {
            await transaction.RollbackAsync(cancellationToken);
            throw;
        }
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error updating booking {BookingId}", bookingId);
        return Result.Failure<UpdateBookingResponse>(Error.Internal("Error updating booking"));
    }
}
```

### Phase 4: Add Comprehensive Validation

#### 4.1 Status Validation
```csharp
private static Result ValidateBookingStatusForUpdate(BookingStatusType? currentStatus)
{
    var updatableStatuses = new[]
    {
        BookingStatusType.Requested,
        BookingStatusType.Accepted,
        BookingStatusType.Confirmed
    };

    if (currentStatus == null || !updatableStatuses.Contains(currentStatus.Value))
    {
        return Result.Failure(Error.Conflict(
            $"Cannot update booking with status: {currentStatus?.GetDescription() ?? "Unknown"}"));
    }

    return Result.Success();
}
```

#### 4.2 Provider Availability Validation
```csharp
private async Task<Result> ValidateProviderAvailabilityAsync(
    CareProviderProfile provider,
    List<BookingWindowRequest> windows,
    CancellationToken cancellationToken)
{
    // Load provider availability with slots
    var availabilities = await _context.Availabilities
        .Include(a => a.AvailabilitySlots)
        .Where(a => a.ProviderId == provider.Id)
        .ToListAsync(cancellationToken);

    var availabilityMap = availabilities.ToDictionary(a => a.DayOfWeek, a => a);

    foreach (var window in windows)
    {
        var dayOfWeek = window.Date.DayOfWeek.ToString();
        
        if (!availabilityMap.TryGetValue(dayOfWeek, out var availability) || !availability.IsAvailable)
        {
            return Result.Failure(Error.Conflict(
                $"Provider is not available on {window.Date:yyyy-MM-dd} ({dayOfWeek})"));
        }

        var fitsInSlot = availability.AvailabilitySlots.Any(slot =>
            slot.StartTime <= window.StartTime && slot.EndTime >= window.EndTime);

        if (!fitsInSlot)
        {
            return Result.Failure(Error.Conflict(
                $"Time {window.StartTime:hh\\:mm}–{window.EndTime:hh\\:mm} on {window.Date:yyyy-MM-dd} " +
                "falls outside provider's available hours"));
        }
    }

    return Result.Success();
}
```

#### 4.3 Booking Conflict Validation
```csharp
private async Task<Result> ValidateBookingConflictsAsync(
    Guid providerId,
    List<BookingWindowRequest> windows,
    Guid excludeBookingId,
    CancellationToken cancellationToken)
{
    var activeStatuses = new[]
    {
        BookingStatusType.Requested,
        BookingStatusType.Accepted,
        BookingStatusType.InProgress,
        BookingStatusType.Confirmed
    };

    var requestedDates = windows.Select(w => DateOnly.FromDateTime(w.Date)).Distinct().ToList();

    var existingWindows = await _context.Bookings
        .Where(b => b.ProviderId == providerId 
                   && b.Id != excludeBookingId 
                   && activeStatuses.Contains(b.Status!.Status))
        .SelectMany(b => b.BookingWindows)
        .Where(bw => requestedDates.Contains(bw.Date))
        .ToListAsync(cancellationToken);

    foreach (var window in windows)
    {
        var windowDate = DateOnly.FromDateTime(window.Date);
        var conflicts = existingWindows
            .Where(existing => existing.Date == windowDate)
            .Where(existing => window.StartTime < existing.EndTime && window.EndTime > existing.StartTime)
            .ToList();

        if (conflicts.Any())
        {
            var conflict = conflicts.First();
            return Result.Failure(Error.Conflict(
                $"Provider has conflicting booking on {window.Date:yyyy-MM-dd} " +
                $"from {conflict.StartTime:hh\\:mm} to {conflict.EndTime:hh\\:mm}"));
        }
    }

    return Result.Success();
}
```

### Phase 5: Testing and Integration

#### 5.1 Unit Tests
- UpdateBookingWithWindowsRequestValidator tests
- UpdateBookingWithWindowsCommandHandler tests
- BookingService.UpdateBookingWithWindowsAsync tests
- Validation method tests

#### 5.2 Integration Tests
- End-to-end booking update scenarios
- Conflict detection scenarios
- Authorization scenarios
- Error handling scenarios

#### 5.3 Controller Integration
```csharp
[HttpPut(ApiRoutes.Bookings.UpdateBookingWithWindows)]
public async Task<IActionResult> UpdateBookingWithWindows(
    [FromRoute] Guid bookingId,
    [FromBody] UpdateBookingWithWindowsRequest request)
{
    var userId = GetCurrentUserId();
    var command = new UpdateBookingWithWindowsCommand(userId, bookingId, request);
    var result = await _mediator.Send(command);
    
    return result.IsSuccess 
        ? Ok(result.Value) 
        : HandleError(result.Error);
}
```

## Migration Strategy

### Backward Compatibility
- Keep existing UpdateBookingRequest/Command for simple single-window updates
- Add new UpdateBookingWithWindowsRequest/Command for multi-window updates
- Gradually migrate clients to use new endpoints

### Database Considerations
- No schema changes required (BookingWindow table already supports multiple windows)
- Existing data remains compatible

## Success Criteria

1. ✅ Support updating bookings with multiple time windows
2. ✅ Proper conflict detection with existing bookings
3. ✅ Provider availability validation
4. ✅ Automatic financial calculations
5. ✅ Comprehensive validation and error handling
6. ✅ Maintain backward compatibility
7. ✅ Transaction safety for data consistency
8. ✅ Proper authorization checks
9. ✅ Status-based update restrictions
10. ✅ Comprehensive test coverage
