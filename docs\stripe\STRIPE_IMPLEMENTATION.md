# Stripe Implementation Document

## Overview

This document outlines the implementation of Stripe payment processing for the SuperCare application. The integration will handle secure payments between clients and care providers with platform fee collection, following the design specifications outlined in the existing documentation.

## Requirements Implementation

Based on the specified requirements, this implementation will cover:

1. Provider onboarding to Stripe Connect → Custom accounts
2. Clients can add and save payment methods → Card storage via Stripe's Customer API
3. Payment only occurs after BookingWindow completion
4. Split payment between platform (owner) and care provider → Stripe Connect Transfers

## 1. Provider Onboarding to Stripe Connect (Custom Accounts)

### Implementation Details

Providers need to be onboarded to Stripe Connect using Custom accounts to allow the platform to have more control over the onboarding experience and maintain consistent branding.

### Key Components

#### StripeAccount Entity
```csharp
public class StripeAccount : BaseEntity
{
    public Guid UserId { get; set; }
    public string StripeAccountId { get; set; } = string.Empty;
    public string AccountType { get; set; } = "custom"; // Changed from "express" to "custom"
    public StripeAccountStatus Status { get; set; } = StripeAccountStatus.Pending;
    public bool ChargesEnabled { get; set; }
    public bool PayoutsEnabled { get; set; }
    public DateTime? OnboardingCompletedAt { get; set; }
    public string? OnboardingUrl { get; set; }
    public string? BusinessType { get; set; }
    public string? Country { get; set; }
    public string? Currency { get; set; }
    public string? Email { get; set; }
    public DateTime? KYCVerifiedAt { get; set; }
    public string? KYCStatus { get; set; }
    public string? RequirementsJson { get; set; } // JSON of pending requirements
    
    // Navigation properties
    public ApplicationUser User { get; set; } = null!;
    public ICollection<BankAccount> BankAccounts { get; set; } = new List<BankAccount>();
    public ICollection<Payout> Payouts { get; set; } = new List<Payout>();
    
    // Computed properties
    public bool IsFullyOnboarded => ChargesEnabled && PayoutsEnabled && KYCVerifiedAt.HasValue;
    public bool CanReceivePayouts => PayoutsEnabled && BankAccounts.Any(ba => ba.IsDefault);
}
```

#### Custom Account Creation Process

1. **Account Creation**: When a provider registers, a Custom Stripe account is created programmatically
2. **Onboarding Flow**: Providers are redirected to a custom onboarding form hosted by SuperCare
3. **Identity Verification**: Integration with Stripe Identity for KYC verification
4. **Bank Account Setup**: Providers can add bank account details for payouts

#### Implementation Code

```csharp
public interface IKYCService
{
    Task<Result<StripeAccount>> CreateCustomConnectAccountAsync(Guid userId, CreateCustomConnectAccountRequest request, CancellationToken ct = default);
    Task<Result<string>> GetCustomOnboardingUrlAsync(Guid userId, CancellationToken ct = default);
    Task<Result<bool>> UpdateCustomAccountRequirementsAsync(string accountId, Dictionary<string, object> requirements, CancellationToken ct = default);
}

public record CreateCustomConnectAccountRequest(
    string BusinessType,
    string Country,
    string Email,
    string FirstName,
    string LastName,
    Address BusinessAddress,
    DateTime? DateOfBirth = null,
    string? PhoneNumber = null
);

internal class KYCService : IKYCService
{
    private readonly StripeClient _stripeClient;
    private readonly ApplicationDbContext _context;
    private readonly ILogger<KYCService> _logger;
    private readonly IOptions<StripeSettings> _settings;

    public async Task<Result<StripeAccount>> CreateCustomConnectAccountAsync(Guid userId, CreateCustomConnectAccountRequest request, CancellationToken ct = default)
    {
        try
        {
            var user = await _context.Users
                .Include(u => u.UserProfile)
                .FirstOrDefaultAsync(u => u.Id == userId, ct);

            if (user == null)
                return Result.Failure<StripeAccount>(Error.NotFound("User not found"));

            // Check if account already exists
            var existingAccount = await _context.StripeAccounts
                .FirstOrDefaultAsync(sa => sa.UserId == userId, ct);

            if (existingAccount != null)
                return Result.Failure<StripeAccount>(Error.Conflict("Stripe account already exists"));

            // Create Custom Stripe Connect account
            var accountOptions = new AccountCreateOptions
            {
                Type = "custom",
                Country = request.Country,
                Email = request.Email,
                BusinessType = request.BusinessType,
                BusinessProfile = new AccountBusinessProfileOptions
                {
                    Name = $"{request.FirstName} {request.LastName}",
                    // SupportUrl = "https://supercare.com/support"
                },
                Capabilities = new AccountCapabilitiesOptions
                {
                    CardPayments = new AccountCapabilitiesCardPaymentsOptions { Requested = true },
                    Transfers = new AccountCapabilitiesTransfersOptions { Requested = true }
                },
                TosAcceptance = new AccountTosAcceptanceOptions
                {
                    Date = DateTime.UtcNow,
                    Ip = "127.0.0.1" // This would be captured from the request context
                },
                Individual = new AccountIndividualOptions
                {
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    Email = request.Email,
                    Address = new AddressOptions
                    {
                        Line1 = request.BusinessAddress.Line1,
                        Line2 = request.BusinessAddress.Line2,
                        City = request.BusinessAddress.City,
                        State = request.BusinessAddress.State,
                        PostalCode = request.BusinessAddress.PostalCode,
                        Country = request.BusinessAddress.Country
                    },
                    Dob = request.DateOfBirth.HasValue ? new DobOptions
                    {
                        Day = request.DateOfBirth.Value.Day,
                        Month = request.DateOfBirth.Value.Month,
                        Year = request.DateOfBirth.Value.Year
                    } : null,
                    Phone = request.PhoneNumber
                },
                Metadata = new Dictionary<string, string>
                {
                    ["user_id"] = userId.ToString(),
                    ["platform"] = "supercare"
                }
            };

            var accountService = new AccountService(_stripeClient);
            var stripeAccount = await accountService.CreateAsync(accountOptions, cancellationToken: ct);

            // Save to database
            var account = new StripeAccount
            {
                UserId = userId,
                StripeAccountId = stripeAccount.Id,
                AccountType = "custom",
                Status = StripeAccountStatus.Pending,
                ChargesEnabled = stripeAccount.ChargesEnabled,
                PayoutsEnabled = stripeAccount.PayoutsEnabled,
                BusinessType = request.BusinessType,
                Country = request.Country,
                Email = request.Email,
                CreatedAt = DateTime.UtcNow
            };

            await _context.StripeAccounts.AddAsync(account, ct);
            await _context.SaveChangesAsync(ct);

            _logger.LogInformation("Custom Stripe Connect account created: {AccountId} for user {UserId}", 
                stripeAccount.Id, userId);

            return Result.Success(account);
        }
        catch (StripeException ex)
        {
            _logger.LogError(ex, "Stripe error creating Custom Connect account for user {UserId}", userId);
            return Result.Failure<StripeAccount>(Error.External($"Account creation error: {ex.Message}"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating Custom Connect account for user {UserId}", userId);
            return Result.Failure<StripeAccount>(Error.Internal("Error creating account"));
        }
    }

    public async Task<Result<string>> GetCustomOnboardingUrlAsync(Guid userId, CancellationToken ct = default)
    {
        try
        {
            var account = await _context.StripeAccounts
                .FirstOrDefaultAsync(sa => sa.UserId == userId, ct);

            if (account == null)
                return Result.Failure<string>(Error.NotFound("Stripe account not found"));

            // For Custom accounts, we'll build our own onboarding form
            // But we can still use AccountLink for specific actions like updating bank accounts
            var options = new AccountLinkCreateOptions
            {
                Account = account.StripeAccountId,
                RefreshUrl = $"{_settings.Value.BaseUrl}/kyc/refresh",
                ReturnUrl = $"{_settings.Value.BaseUrl}/kyc/complete",
                Type = "account_onboarding"
            };

            var service = new AccountLinkService(_stripeClient);
            var accountLink = await service.CreateAsync(options, cancellationToken: ct);

            // Update account with onboarding URL
            account.OnboardingUrl = accountLink.Url;
            await _context.SaveChangesAsync(ct);

            return Result.Success(accountLink.Url);
        }
        catch (StripeException ex)
        {
            _logger.LogError(ex, "Stripe error getting onboarding URL for user {UserId}", userId);
            return Result.Failure<string>(Error.External($"Onboarding error: {ex.Message}"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting onboarding URL for user {UserId}", userId);
            return Result.Failure<string>(Error.Internal("Error getting onboarding URL"));
        }
    }
}
```

### Custom Account Requirements Collection

For Custom accounts, the platform collects all required information directly through its own forms:

1. **Personal Information**: Legal name, date of birth, address
2. **Business Information**: Business type, tax ID, business address
3. **Bank Account Details**: For receiving payouts
4. **Identity Verification**: Document upload for identity verification

## 2. Client Payment Method Storage via Stripe Customer API

### Implementation Details

Clients can securely add and save payment methods using Stripe's Customer API, which tokenizes card information and stores it securely on Stripe's servers.

### Key Components

#### PaymentMethod Entity
```csharp
public class PaymentMethod : BaseEntity
{
    public Guid UserId { get; set; }
    public string StripePaymentMethodId { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // card, bank_account, etc.
    public string Last4 { get; set; } = string.Empty;
    public string? Brand { get; set; }
    public int? ExpiryMonth { get; set; }
    public int? ExpiryYear { get; set; }
    public string? Country { get; set; }
    public bool IsDefault { get; set; }
    public bool IsExpired { get; set; }
    public DateTime? LastUsedAt { get; set; }
    
    // Navigation properties
    public ApplicationUser User { get; set; } = null!;
    public ICollection<Payment> Payments { get; set; } = new List<Payment>();
}
```

#### Customer Management

Each user in the system has a corresponding Customer object in Stripe:

```csharp
public interface IStripePaymentService
{
    Task<Result<PaymentMethod>> AttachPaymentMethodAsync(string paymentMethodId, Guid userId, CancellationToken ct = default);
    Task<Result<bool>> DetachPaymentMethodAsync(string paymentMethodId, CancellationToken ct = default);
    Task<Result<List<PaymentMethod>>> GetPaymentMethodsAsync(Guid userId, CancellationToken ct = default);
    Task<Result<PaymentMethod>> SetDefaultPaymentMethodAsync(string paymentMethodId, Guid userId, CancellationToken ct = default);
}

internal class StripePaymentService : IStripePaymentService
{
    private readonly StripeClient _stripeClient;
    private readonly ApplicationDbContext _context;
    private readonly ILogger<StripePaymentService> _logger;
    private readonly IOptions<StripeSettings> _settings;

    private async Task<string> GetOrCreateStripeCustomerAsync(Guid userId, CancellationToken ct)
    {
        var user = await _context.Users
            .Include(u => u.UserProfile)
            .FirstOrDefaultAsync(u => u.Id == userId, ct);

        if (user == null)
            throw new InvalidOperationException("User not found");

        // Check if customer already exists
        if (!string.IsNullOrEmpty(user.StripeCustomerId))
            return user.StripeCustomerId;

        // Create new Stripe customer
        var customerOptions = new CustomerCreateOptions
        {
            Email = user.Email,
            Name = $"{user.UserProfile?.FirstName} {user.UserProfile?.LastName}".Trim(),
            Phone = user.PhoneNumber,
            Metadata = new Dictionary<string, string>
            {
                ["user_id"] = userId.ToString()
            }
        };

        var customerService = new CustomerService(_stripeClient);
        var customer = await customerService.CreateAsync(customerOptions, cancellationToken: ct);


// Update user with Stripe customer ID
        user.StripeCustomerId = customer.Id;
        await _context.SaveChangesAsync(ct);

        return customer.Id;
    }
}
```

## 3. Payment Processing after BookingWindow Completion

### Implementation Details

Payments are processed only after the `BookingWindow` is completed. This ensures that clients are charged only for services that have been rendered.

### Key Components

#### Payment Entity
```csharp
public class Payment : BaseEntity
{
    // ... existing properties
    public string? StripePaymentIntentId { get; set; }
    public bool IsHeld { get; set; } // For escrow functionality
    public DateTime? HoldUntil { get; set; }
    public DateTime? ReleasedAt { get; set; }
}
```

#### Payment Processing Flow

1. **Authorize Payment**: When a booking is confirmed, a payment intent is created with `capture_method` set to `manual`. This authorizes the payment but does not capture the funds.
2. **Hold Funds**: The funds are held until the `BookingWindow` is completed.
3. **Capture Payment**: After the `BookingWindow` is completed, the payment is captured.

#### Implementation Code
```csharp
public interface IStripePaymentService
{
    // ... existing methods
    Task<Result<PaymentIntent>> CapturePaymentAsync(string paymentIntentId, CancellationToken ct = default);
}

internal class StripePaymentService : IStripePaymentService
{
    // ... existing implementation

    public async Task<Result<PaymentIntent>> CapturePaymentAsync(string paymentIntentId, CancellationToken ct = default)
    {
        try
        {
            var service = new PaymentIntentService(_stripeClient);
            var paymentIntent = await service.CaptureAsync(paymentIntentId, cancellationToken: ct);

            // Update payment record
            var payment = await _context.Payments
                .FirstOrDefaultAsync(p => p.StripePaymentIntentId == paymentIntentId, ct);

            if (payment != null)
            {
                payment.Status = PaymentStatus.Completed;
                payment.ReleasedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync(ct);
            }

            return Result.Success(paymentIntent);
        }
        catch (StripeException ex)
        {
            _logger.LogError(ex, "Stripe error capturing payment for intent {PaymentIntentId}", paymentIntentId);
            return Result.Failure<PaymentIntent>(Error.External($"Payment capture error: {ex.Message}"));
        }
    }
}
```

## 4. Split Payment Between Platform and Care Provider

### Implementation Details

Split payments are handled using Stripe Connect Transfers, which allows the platform to take a fee from each transaction and transfer the rest to the care provider.

### Key Components

#### Payment Entity
```csharp
public class Payment : BaseEntity
{
    // ... existing properties
    public decimal PlatformFeeAmount { get; set; }
    public decimal ProviderAmount { get; set; }
    public string? TransferGroupId { get; set; }
}
```

#### Split Payment Flow

1. **Calculate Fees**: When a payment intent is created, the platform fee is calculated.
2. **Create Transfer**: The payment intent is created with a `transfer_data` object that specifies the destination (the provider's Stripe account) and the amount to transfer.
3. **Transfer Funds**: When the payment is captured, the funds are automatically transferred to the provider's account, and the platform fee is retained by the platform.

#### Implementation Code
```csharp
internal class StripePaymentService : IStripePaymentService
{
    public async Task<Result<PaymentIntent>> CreatePaymentIntentAsync(CreatePaymentIntentRequest request, CancellationToken ct = default)
    {
        // ... existing implementation
        var platformFeeAmount = CalculatePlatformFee(request.Amount, booking.CategoryId);
        var providerAmount = request.Amount - platformFeeAmount;
        var transferGroup = $"booking_{request.BookingId}_{DateTime.UtcNow.Ticks}";

        var paymentIntentOptions = new PaymentIntentCreateOptions
        {
            // ... existing options
            ApplicationFeeAmount = (long)(platformFeeAmount * 100),
            TransferData = new PaymentIntentTransferDataOptions
            {
                Destination = providerAccount.StripeAccountId
            },
            TransferGroup = transferGroup,
        };
        // ... rest of implementation
    }
}
```

## Final Document Structure
The final document will be structured with a clear overview, detailed implementation steps for each requirement, and code examples to guide developers. The document will align with the existing `DESIGN.md` and `REQUIREMENTS.md` to ensure consistency.
