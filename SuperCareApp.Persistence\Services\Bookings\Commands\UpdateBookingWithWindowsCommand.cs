using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Bookings;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Bookings.Commands;

public record UpdateBookingWithWindowsCommand(
    Guid UserId,
    Guid BookingId,
    UpdateBookingWithWindowsRequest Request
) : ICommand<Result<UpdateBookingResponse>>;

internal sealed class UpdateBookingWithWindowsCommandHandler
    : ICommandHandler<UpdateBookingWithWindowsCommand, Result<UpdateBookingResponse>>
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<UpdateBookingWithWindowsCommandHandler> _logger;

    public UpdateBookingWithWindowsCommandHandler(
        ApplicationDbContext context,
        ILogger<UpdateBookingWithWindowsCommandHandler> logger
    )
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Result<UpdateBookingResponse>> Handle(
        UpdateBookingWithWindowsCommand command,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var (userId, bookingId, request) = command;

            // 1. Load existing booking with all related data
            var booking = await _context
                .Bookings.Include(b => b.BookingWindows)
                .Include(b => b.Status)
                .FirstOrDefaultAsync(b => b.Id == bookingId && !b.IsDeleted, cancellationToken);

            if (booking == null)
            {
                return Result.Failure<UpdateBookingResponse>(Error.NotFound("Booking not found"));
            }

            var providerId = booking.ProviderId;

            // 2. Validate user authorization
            if (!IsUserAuthorizedForBooking(userId, booking.ClientId, booking.ProviderId))
            {
                return Result.Failure<UpdateBookingResponse>(
                    Error.Unauthorized("User is not authorized to update this booking")
                );
            }

            // 3. Validate booking status (can only update certain statuses)
            var statusValidation = ValidateBookingStatusForUpdate(booking.Status?.Status);
            if (!statusValidation.IsSuccess)
            {
                return Result.Failure<UpdateBookingResponse>(statusValidation.Error);
            }

            // 4. Validate entities exist and load provider with availability
            var provider = await _context
                .CareProviderProfiles.Include(p => p.Availabilities)
                .ThenInclude(a => a.AvailabilitySlots)
                .FirstOrDefaultAsync(p => p.Id == providerId, cancellationToken);

            if (provider == null)
            {
                return Result.Failure<UpdateBookingResponse>(Error.NotFound("Provider not found"));
            }

            // 5. Validate intra-request window overlaps
            var windowValidation = ValidateBookingWindows(request.BookingWindows);
            if (!windowValidation.IsSuccess)
            {
                return Result.Failure<UpdateBookingResponse>(windowValidation.Error);
            }

            // 6. Validate provider availability for new windows
            var availabilityValidation = await ValidateProviderAvailabilityAsync(
                provider,
                request.BookingWindows,
                cancellationToken
            );
            if (!availabilityValidation.IsSuccess)
            {
                return Result.Failure<UpdateBookingResponse>(availabilityValidation.Error);
            }

            // 7. Check conflicts with other bookings (excluding current booking)
            var conflictValidation = await ValidateBookingConflictsAsync(
                providerId,
                request.BookingWindows,
                bookingId,
                cancellationToken
            );
            if (!conflictValidation.IsSuccess)
            {
                return Result.Failure<UpdateBookingResponse>(conflictValidation.Error);
            }

            var categoryId = booking.CategoryId;
            var category = await _context.CareCategories.FirstOrDefaultAsync(
                c => c.Id == categoryId && !c.IsDeleted,
                cancellationToken
            );

            // 8. Calculate new amounts
            var amounts = CalculateBookingAmounts(provider, category, request.BookingWindows);

            // 9. Update booking in transaction
            await using var transaction = await _context.Database.BeginTransactionAsync(
                cancellationToken
            );
            try
            {
                await UpdateBookingEntityAsync(
                    booking,
                    request,
                    amounts,
                    userId,
                    cancellationToken
                );
                await CreateBookingStatusHistoryAsync(
                    booking.Id,
                    userId,
                    "Booking updated",
                    cancellationToken
                );

                await _context.SaveChangesAsync(cancellationToken);
                await transaction.CommitAsync(cancellationToken);

                // 10. Return response
                var response = CreateUpdateBookingResponse(booking, amounts);
                return Result.Success(response);
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error updating booking {BookingId} for user {UserId}",
                command.BookingId,
                command.UserId
            );
            return Result.Failure<UpdateBookingResponse>(Error.Internal("Error updating booking"));
        }
    }

    private static bool IsUserAuthorizedForBooking(Guid userId, Guid clientId, Guid providerId)
    {
        return userId == clientId || userId == providerId;
    }

    private static Result ValidateBookingStatusForUpdate(BookingStatusType? currentStatus)
    {
        var updatableStatuses = new[]
        {
            BookingStatusType.Requested,
            BookingStatusType.Accepted,
            BookingStatusType.Confirmed,
            BookingStatusType.Modified,
        };

        if (currentStatus == null || !updatableStatuses.Contains(currentStatus.Value))
        {
            return Result.Failure(
                Error.Conflict(
                    $"Cannot update booking with status: {currentStatus?.GetDescription() ?? "Unknown"}"
                )
            );
        }

        return Result.Success();
    }

    private static Result ValidateBookingWindows(List<BookingWindowRequest> windows)
    {
        if (windows == null || !windows.Any())
        {
            return Result.Failure(Error.Validation("At least one booking window is required"));
        }

        // Check for overlaps within the same request
        var windowsByDate = windows.GroupBy(w => w.Date.Date);

        foreach (var dayGroup in windowsByDate)
        {
            var sortedWindows = dayGroup.OrderBy(w => w.StartTime).ToList();
            for (int i = 1; i < sortedWindows.Count; i++)
            {
                if (sortedWindows[i].StartTime < sortedWindows[i - 1].EndTime)
                {
                    return Result.Failure(
                        Error.Conflict(
                            $"Booking windows overlap within the same request on {dayGroup.Key:yyyy-MM-dd}"
                        )
                    );
                }
            }
        }

        return Result.Success();
    }

    private async Task<Result> ValidateProviderAvailabilityAsync(
        CareProviderProfile provider,
        List<BookingWindowRequest> windows,
        CancellationToken cancellationToken
    )
    {
        var requestedDays = windows.Select(w => w.Date.DayOfWeek.ToString()).Distinct().ToList();
        var availabilityMap = provider
            .Availabilities.Where(a => requestedDays.Contains(a.DayOfWeek))
            .ToDictionary(a => a.DayOfWeek, a => a);

        foreach (var window in windows)
        {
            var dayOfWeek = window.Date.DayOfWeek.ToString();

            if (
                !availabilityMap.TryGetValue(dayOfWeek, out var availability)
                || !availability.IsAvailable
            )
            {
                return Result.Failure(
                    Error.Conflict(
                        $"Provider is not available on {window.Date:yyyy-MM-dd} ({dayOfWeek})"
                    )
                );
            }

            var fitsInSlot = availability.AvailabilitySlots.Any(slot =>
                slot.StartTime <= window.StartTime && slot.EndTime >= window.EndTime
            );

            if (!fitsInSlot)
            {
                return Result.Failure(
                    Error.Conflict(
                        $"Time {window.StartTime:hh\\:mm}–{window.EndTime:hh\\:mm} on {window.Date:yyyy-MM-dd} "
                            + "falls outside provider's available hours"
                    )
                );
            }
        }

        await Task.Yield();

        return Result.Success();
    }

    private async Task<Result> ValidateBookingConflictsAsync(
        Guid providerId,
        List<BookingWindowRequest> windows,
        Guid excludeBookingId,
        CancellationToken cancellationToken
    )
    {
        var activeStatuses = new[]
        {
            BookingStatusType.Requested,
            BookingStatusType.Accepted,
            BookingStatusType.InProgress,
            BookingStatusType.Confirmed,
        };

        var requestedDates = windows.Select(w => DateOnly.FromDateTime(w.Date)).Distinct().ToList();

        var existingWindows = await _context
            .Bookings.Where(b =>
                b.ProviderId == providerId
                && b.Id != excludeBookingId
                && activeStatuses.Contains(b.Status!.Status)
            )
            .SelectMany(b => b.BookingWindows)
            .Where(bw => requestedDates.Contains(bw.Date))
            .ToListAsync(cancellationToken);

        foreach (var window in windows)
        {
            var windowDate = DateOnly.FromDateTime(window.Date);
            var conflicts = existingWindows
                .Where(existing => existing.Date == windowDate)
                .Where(existing =>
                    window.StartTime < existing.EndTime && window.EndTime > existing.StartTime
                )
                .ToList();

            if (conflicts.Any())
            {
                var conflict = conflicts.First();
                return Result.Failure(
                    Error.Conflict(
                        $"Provider has conflicting booking on {window.Date:yyyy-MM-dd} "
                            + $"from {conflict.StartTime:hh\\:mm} to {conflict.EndTime:hh\\:mm}"
                    )
                );
            }
        }

        return Result.Success();
    }

    private static (
        decimal TotalAmount,
        decimal PlatformFee,
        decimal ProviderAmount
    ) CalculateBookingAmounts(
        CareProviderProfile provider,
        CareCategory category,
        List<BookingWindowRequest> windows
    )
    {
        var totalHours = windows.Sum(w => (w.EndTime - w.StartTime).TotalHours);
        var totalAmount = provider.HourlyRate * (decimal)totalHours;
        var platformFee = category.PlatformFee;
        var providerAmount = totalAmount / (1 + platformFee);

        return (totalAmount!.Value, platformFee, providerAmount!.Value);
    }

    private async Task UpdateBookingEntityAsync(
        Booking booking,
        UpdateBookingWithWindowsRequest request,
        (decimal TotalAmount, decimal PlatformFee, decimal ProviderAmount) amounts,
        Guid userId,
        CancellationToken cancellationToken
    )
    {
        // Remove existing booking windows
        _context.BookingWindows.RemoveRange(booking.BookingWindows);

        // Create new booking windows
        var newWindows = request
            .BookingWindows.Select(w => new BookingWindow
            {
                BookingId = booking.Id,
                Date = DateOnly.FromDateTime(w.Date),
                StartTime = w.StartTime,
                EndTime = w.EndTime,
                CreatedBy = userId,
                CreatedAt = DateTime.UtcNow,
            })
            .ToList();

        booking.BookingWindows = newWindows;

        // Update booking properties
        booking.SpecialInstructions = request.SpecialInstructions;
        booking.TotalAmount = amounts.TotalAmount;
        booking.PlatformFee = amounts.PlatformFee;
        booking.ProviderAmount = amounts.ProviderAmount;
        booking.UpdatedAt = DateTime.UtcNow;
        booking.UpdatedBy = userId;
        await Task.Yield();
        // Recalculate working hours
        var totalHours = request.BookingWindows.Sum(w => (w.EndTime - w.StartTime).TotalHours);
        booking.WorkingHours = (int)Math.Ceiling(totalHours);
    }

    private async Task CreateBookingStatusHistoryAsync(
        Guid bookingId,
        Guid userId,
        string notes,
        CancellationToken cancellationToken
    )
    {
        var statusHistory = new BookingStatus
        {
            Id = Guid.NewGuid(),
            BookingId = bookingId,
            Status = BookingStatusType.Modified,
            CreatedBy = userId,
            Notes = notes,
            CreatedAt = DateTime.UtcNow,
        };

        await _context.BookingStatuses.AddAsync(statusHistory, cancellationToken);
    }

    private static UpdateBookingResponse CreateUpdateBookingResponse(
        Booking booking,
        (decimal TotalAmount, decimal PlatformFee, decimal ProviderAmount) amounts
    )
    {
        return new UpdateBookingResponse
        {
            BookingId = booking.Id,
            Status = BookingStatusType.Modified.GetDescription(),
            TotalAmount = amounts.TotalAmount,
            PlatformFee = amounts.PlatformFee,
            ProviderAmount = amounts.ProviderAmount,
            BookingWindows = booking
                .BookingWindows.Select(w => new BookingWindowResponse
                {
                    Date = w.Date.ToDateTime(w.StartTime),
                    StartTime = w.StartTime.ToTimeSpan(),
                    EndTime = w.EndTime.ToTimeSpan(),
                })
                .ToList(),
            UpdatedAt = booking.UpdatedAt ?? DateTime.UtcNow,
            SpecialInstructions = booking.SpecialInstructions,
            TotalHours = booking.WorkingHours ?? 0,
        };
    }
}
