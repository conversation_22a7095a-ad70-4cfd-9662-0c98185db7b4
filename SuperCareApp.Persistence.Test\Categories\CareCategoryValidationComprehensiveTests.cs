using FluentValidation.TestHelper;
using SuperCareApp.Application.Common.Models.Categories;

namespace SuperCareApp.Persistence.Test.Categories;

/// <summary>
/// Comprehensive validation tests for all care category request models
/// </summary>
public class CareCategoryValidationComprehensiveTests
{
    private readonly CreateCareCategoryRequestValidator _createValidator;
    private readonly UpdateCareCategoryRequestValidation _updateValidator;
    private readonly BulkUpdateCareCategoriesRequestValidator _bulkUpdateValidator;
    private readonly CareCategoryUpdateItemValidator _updateItemValidator;

    public CareCategoryValidationComprehensiveTests()
    {
        _createValidator = new CreateCareCategoryRequestValidator();
        _updateValidator = new UpdateCareCategoryRequestValidation();
        _bulkUpdateValidator = new BulkUpdateCareCategoriesRequestValidator();
        _updateItemValidator = new CareCategoryUpdateItemValidator();
    }

    #region CreateCareCategoryRequest Validation Tests

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public void CreateCareCategoryRequest_WithInvalidName_ShouldFailValidation(string invalidName)
    {
        // Arrange
        var request = new CreateCareCategoryRequest
        {
            Name = invalidName,
            Description = "Valid Description",
            IsActive = true,
            PlatformFee = 10.00m
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void CreateCareCategoryRequest_WithTooLongName_ShouldFailValidation()
    {
        // Arrange
        var longName = new string('a', 101); // Exceeds 100 character limit
        var request = new CreateCareCategoryRequest
        {
            Name = longName,
            Description = "Valid Description",
            IsActive = true,
            PlatformFee = 10.00m
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void CreateCareCategoryRequest_WithMaxLengthName_ShouldPassValidation()
    {
        // Arrange
        var maxLengthName = new string('a', 100); // Exactly 100 characters
        var request = new CreateCareCategoryRequest
        {
            Name = maxLengthName,
            Description = "Valid Description",
            IsActive = true,
            PlatformFee = 10.00m
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void CreateCareCategoryRequest_WithTooLongDescription_ShouldFailValidation()
    {
        // Arrange
        var longDescription = new string('a', 501); // Exceeds 500 character limit
        var request = new CreateCareCategoryRequest
        {
            Name = "Valid Name",
            Description = longDescription,
            IsActive = true,
            PlatformFee = 10.00m
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Description);
    }

    [Fact]
    public void CreateCareCategoryRequest_WithMaxLengthDescription_ShouldPassValidation()
    {
        // Arrange
        var maxLengthDescription = new string('a', 500); // Exactly 500 characters
        var request = new CreateCareCategoryRequest
        {
            Name = "Valid Name",
            Description = maxLengthDescription,
            IsActive = true,
            PlatformFee = 10.00m
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Description);
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(-0.01)]
    public void CreateCareCategoryRequest_WithNegativePlatformFee_ShouldFailValidation(decimal negativeFee)
    {
        // Arrange
        var request = new CreateCareCategoryRequest
        {
            Name = "Valid Name",
            Description = "Valid Description",
            IsActive = true,
            PlatformFee = negativeFee
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.PlatformFee);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(0.01)]
    [InlineData(100.00)]
    [InlineData(999999.99)]
    public void CreateCareCategoryRequest_WithValidPlatformFee_ShouldPassValidation(decimal validFee)
    {
        // Arrange
        var request = new CreateCareCategoryRequest
        {
            Name = "Valid Name",
            Description = "Valid Description",
            IsActive = true,
            PlatformFee = validFee
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.PlatformFee);
    }

    [Fact]
    public void CreateCareCategoryRequest_WithTooLongIcon_ShouldFailValidation()
    {
        // Arrange
        var longIcon = new string('a', 256); // Exceeds 255 character limit
        var request = new CreateCareCategoryRequest
        {
            Name = "Valid Name",
            Description = "Valid Description",
            IsActive = true,
            PlatformFee = 10.00m,
            Icon = longIcon
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Icon);
    }

    [Fact]
    public void CreateCareCategoryRequest_WithMaxLengthIcon_ShouldPassValidation()
    {
        // Arrange
        var maxLengthIcon = new string('a', 255); // Exactly 255 characters
        var request = new CreateCareCategoryRequest
        {
            Name = "Valid Name",
            Description = "Valid Description",
            IsActive = true,
            PlatformFee = 10.00m,
            Icon = maxLengthIcon
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Icon);
    }

    [Theory]
    [InlineData("#FF5733")] // Valid hex color
    [InlineData("#000000")] // Black
    [InlineData("#FFFFFF")] // White
    [InlineData("#123ABC")] // Mixed numbers and letters
    [InlineData("#abc123")] // Lowercase
    public void CreateCareCategoryRequest_WithValidColorFormats_ShouldPassValidation(string validColor)
    {
        // Arrange
        var request = new CreateCareCategoryRequest
        {
            Name = "Valid Name",
            Description = "Valid Description",
            IsActive = true,
            PlatformFee = 10.00m,
            Color = validColor
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Color);
    }

    [Theory]
    [InlineData("FF5733")] // Missing #
    [InlineData("#FF573")] // Too short
    [InlineData("#FF57333")] // Too long
    [InlineData("#GGFF00")] // Invalid hex characters
    [InlineData("red")] // Not hex format
    [InlineData("#FF5733X")] // Too long with invalid character
    public void CreateCareCategoryRequest_WithInvalidColorFormats_ShouldFailValidation(string invalidColor)
    {
        // Arrange
        var request = new CreateCareCategoryRequest
        {
            Name = "Valid Name",
            Description = "Valid Description",
            IsActive = true,
            PlatformFee = 10.00m,
            Color = invalidColor
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Color);
    }

    #endregion

    #region UpdateCareCategoryRequest Validation Tests

    [Fact]
    public void UpdateCareCategoryRequest_WithEmptyName_ShouldFailValidation()
    {
        // Arrange
        var request = new UpdateCareCategoryRequest
        {
            Name = "",
            Description = "Valid Description"
        };

        // Act & Assert
        var result = _updateValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void UpdateCareCategoryRequest_WithNullName_ShouldNotPassValidation()
    {
        // Arrange
        var request = new UpdateCareCategoryRequest
        {
            Name = null,
            Description = "Valid Description"
        };

        // Act & Assert
        var result = _updateValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void UpdateCareCategoryRequest_WithTooLongName_ShouldFailValidation()
    {
        // Arrange
        var longName = new string('a', 101); // Exceeds 100 character limit
        var request = new UpdateCareCategoryRequest
        {
            Name = longName,
            Description = "Valid Description"
        };

        // Act & Assert
        var result = _updateValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void UpdateCareCategoryRequest_WithTooLongDescription_ShouldFailValidation()
    {
        // Arrange
        var longDescription = new string('a', 501); // Exceeds 500 character limit
        var request = new UpdateCareCategoryRequest
        {
            Name = "Valid Name",
            Description = longDescription
        };

        // Act & Assert
        var result = _updateValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Description);
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(-0.01)]
    public void UpdateCareCategoryRequest_WithNegativePlatformFee_ShouldFailValidation(decimal negativeFee)
    {
        // Arrange
        var request = new UpdateCareCategoryRequest
        {
            Name = "Valid Name",
            PlatformFee = negativeFee
        };

        // Act & Assert
        var result = _updateValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.PlatformFee);
    }

    [Fact]
    public void UpdateCareCategoryRequest_WithNullPlatformFee_ShouldPassValidation()
    {
        // Arrange
        var request = new UpdateCareCategoryRequest
        {
            Name = "Valid Name",
            PlatformFee = null
        };

        // Act & Assert
        var result = _updateValidator.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.PlatformFee);
    }

    [Fact]
    public void UpdateCareCategoryRequest_WithTooLongIcon_ShouldFailValidation()
    {
        // Arrange
        var longIcon = new string('a', 256); // Exceeds 255 character limit
        var request = new UpdateCareCategoryRequest
        {
            Name = "Valid Name",
            Icon = longIcon
        };

        // Act & Assert
        var result = _updateValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Icon);
    }

    [Theory]
    [InlineData("FF5733")] // Missing #
    [InlineData("#FF573")] // Too short
    [InlineData("#FF57333")] // Too long
    [InlineData("#GGFF00")] // Invalid hex characters
    [InlineData("red")] // Not hex format
    public void UpdateCareCategoryRequest_WithInvalidColorFormats_ShouldFailValidation(string invalidColor)
    {
        // Arrange
        var request = new UpdateCareCategoryRequest
        {
            Name = "Valid Name",
            Color = invalidColor
        };

        // Act & Assert
        var result = _updateValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Color);
    }

    #endregion

    #region BulkUpdateCareCategoriesRequest Validation Tests
    

    [Fact]
    public void BulkUpdateCareCategoriesRequest_WithTooManyCategories_ShouldFailValidation()
    {
        // Arrange
        var categories = Enumerable.Range(1, 101).Select(i => new CareCategoryUpdateItem
        {
            Id = Guid.NewGuid(),
            Name = $"Category {i}"
        }).ToList();

        var request = new BulkUpdateCareCategoriesRequest
        {
            Categories = categories
        };

        // Act & Assert
        var result = _bulkUpdateValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Categories);
    }

    [Fact]
    public void BulkUpdateCareCategoriesRequest_WithMaxAllowedCategories_ShouldPassValidation()
    {
        // Arrange
        var categories = Enumerable.Range(1, 100).Select(i => new CareCategoryUpdateItem
        {
            Id = Guid.NewGuid(),
            Name = $"Category {i}"
        }).ToList();

        var request = new BulkUpdateCareCategoriesRequest
        {
            Categories = categories
        };

        // Act & Assert
        var result = _bulkUpdateValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Categories);
    }

    [Fact]
    public void BulkUpdateCareCategoriesRequest_WithDuplicateIds_ShouldFailValidation()
    {
        // Arrange
        var duplicateId = Guid.NewGuid();
        var categories = new List<CareCategoryUpdateItem>
        {
            new CareCategoryUpdateItem { Id = duplicateId, Name = "Category 1" },
            new CareCategoryUpdateItem { Id = duplicateId, Name = "Category 2" }
        };

        var request = new BulkUpdateCareCategoriesRequest
        {
            Categories = categories
        };

        // Act & Assert
        var result = _bulkUpdateValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Categories);
    }

    [Fact]
    public void BulkUpdateCareCategoriesRequest_WithUniqueIds_ShouldPassValidation()
    {
        // Arrange
        var categories = new List<CareCategoryUpdateItem>
        {
            new CareCategoryUpdateItem { Id = Guid.NewGuid(), Name = "Category 1" },
            new CareCategoryUpdateItem { Id = Guid.NewGuid(), Name = "Category 2" }
        };

        var request = new BulkUpdateCareCategoriesRequest
        {
            Categories = categories
        };

        // Act & Assert
        var result = _bulkUpdateValidator.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Categories);
    }

    #endregion

    #region CareCategoryUpdateItem Validation Tests

    [Fact]
    public void CareCategoryUpdateItem_WithEmptyId_ShouldFailValidation()
    {
        // Arrange
        var item = new CareCategoryUpdateItem
        {
            Id = Guid.Empty,
            Name = "Valid Name"
        };

        // Act & Assert
        var result = _updateItemValidator.TestValidate(item);
        result.ShouldHaveValidationErrorFor(x => x.Id);
    }

    [Fact]
    public void CareCategoryUpdateItem_WithValidId_ShouldPassValidation()
    {
        // Arrange
        var item = new CareCategoryUpdateItem
        {
            Id = Guid.NewGuid(),
            Name = "Valid Name"
        };

        // Act & Assert
        var result = _updateItemValidator.TestValidate(item);
        result.ShouldNotHaveValidationErrorFor(x => x.Id);
    }

    [Fact]
    public void CareCategoryUpdateItem_WithNullName_ShouldPassValidation()
    {
        // Arrange
        var item = new CareCategoryUpdateItem
        {
            Id = Guid.NewGuid(),
            Name = null
        };

        // Act & Assert
        var result = _updateItemValidator.TestValidate(item);
        result.ShouldNotHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void CareCategoryUpdateItem_WithTooLongName_ShouldFailValidation()
    {
        // Arrange
        var longName = new string('a', 101); // Exceeds 100 character limit
        var item = new CareCategoryUpdateItem
        {
            Id = Guid.NewGuid(),
            Name = longName
        };

        // Act & Assert
        var result = _updateItemValidator.TestValidate(item);
        result.ShouldHaveValidationErrorFor(x => x.Name);
    }

    [Fact]
    public void CareCategoryUpdateItem_WithTooLongDescription_ShouldFailValidation()
    {
        // Arrange
        var longDescription = new string('a', 501); // Exceeds 500 character limit
        var item = new CareCategoryUpdateItem
        {
            Id = Guid.NewGuid(),
            Name = "Valid Name",
            Description = longDescription
        };

        // Act & Assert
        var result = _updateItemValidator.TestValidate(item);
        result.ShouldHaveValidationErrorFor(x => x.Description);
    }

    [Theory]
    [InlineData(-1)]
    [InlineData(-0.01)]
    public void CareCategoryUpdateItem_WithNegativePlatformFee_ShouldFailValidation(decimal negativeFee)
    {
        // Arrange
        var item = new CareCategoryUpdateItem
        {
            Id = Guid.NewGuid(),
            Name = "Valid Name",
            PlatformFee = negativeFee
        };

        // Act & Assert
        var result = _updateItemValidator.TestValidate(item);
        result.ShouldHaveValidationErrorFor(x => x.PlatformFee);
    }

    [Fact]
    public void CareCategoryUpdateItem_WithNullPlatformFee_ShouldPassValidation()
    {
        // Arrange
        var item = new CareCategoryUpdateItem
        {
            Id = Guid.NewGuid(),
            Name = "Valid Name",
            PlatformFee = null
        };

        // Act & Assert
        var result = _updateItemValidator.TestValidate(item);
        result.ShouldNotHaveValidationErrorFor(x => x.PlatformFee);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(0.01)]
    [InlineData(100.00)]
    public void CareCategoryUpdateItem_WithValidPlatformFee_ShouldPassValidation(decimal validFee)
    {
        // Arrange
        var item = new CareCategoryUpdateItem
        {
            Id = Guid.NewGuid(),
            Name = "Valid Name",
            PlatformFee = validFee
        };

        // Act & Assert
        var result = _updateItemValidator.TestValidate(item);
        result.ShouldNotHaveValidationErrorFor(x => x.PlatformFee);
    }

    #endregion

    #region Edge Case Tests

    [Fact]
    public void CreateCareCategoryRequest_WithAllNullOptionalFields_ShouldPassValidation()
    {
        // Arrange
        var request = new CreateCareCategoryRequest
        {
            Name = "Required Name",
            Description = null,
            IsActive = true,
            PlatformFee = 0m,
            Icon = null,
            Color = null
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void UpdateCareCategoryRequest_WithAllNullFields_ShouldPassValidation()
    {
        // Arrange
        var request = new UpdateCareCategoryRequest
        {
            Name = null,
            Description = null,
            IsActive = null,
            PlatformFee = null,
            Icon = null,
            Color = null
        };

        // Act & Assert
        var result = _updateValidator.TestValidate(request);
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void CareCategoryUpdateItem_WithAllNullOptionalFields_ShouldPassValidation()
    {
        // Arrange
        var item = new CareCategoryUpdateItem
        {
            Id = Guid.NewGuid(),
            Name = null,
            Description = null,
            IsActive = null,
            PlatformFee = null
        };

        // Act & Assert
        var result = _updateItemValidator.TestValidate(item);
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    public void UpdateCareCategoryRequest_WithEmptyStringFields_ShouldFailValidation(string emptyString)
    {
        // Arrange
        var request = new UpdateCareCategoryRequest
        {
            Name = emptyString,
            Description = emptyString,
            Icon = emptyString,
            Color = emptyString
        };

        // Act & Assert
        var result = _updateValidator.TestValidate(request);
        result.ShouldHaveValidationErrorFor(x => x.Name);
        result.ShouldHaveValidationErrorFor(x => x.Description);
        result.ShouldHaveValidationErrorFor(x => x.Icon);
        result.ShouldHaveValidationErrorFor(x => x.Color);
    }

    #endregion
}