using System.Text.Json;
using SuperCareApp.Application.Common.Interfaces.Mediator;
using SuperCareApp.Application.Common.Interfaces.Messages.Command;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Entities.ValueObjects;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Identity.Commands;

public record RegisterUserCommand(
    string Email,
    string Password,
    string? PhoneNumber,
    bool IsCareProvider,
    string? FirstName = null,
    string? LastName = null
) : ICommand<Result<AuthResponse>>;

internal sealed class RegisterUserCommandHandler
    : ICommandHandler<RegisterUserCommand, Result<AuthResponse>>
{
    private readonly IAuthService _authService;
    private readonly ICareProviderProfileService _careProviderProfileService;
    private readonly IApprovalService _approvalService;
    private readonly IAvailabilityTemplateService _availabilityTemplateService;
    private readonly ILogger<RegisterUserCommandHandler> _logger;
    private readonly IMediator _mediator;

    public RegisterUserCommandHandler(
        IAuthService authService,
        ICareProviderProfileService careProviderProfileService,
        IApprovalService approvalService,
        IAvailabilityTemplateService availabilityTemplateService,
        ILogger<RegisterUserCommandHandler> logger,
        IMediator mediator
    )
    {
        _authService = authService;
        _careProviderProfileService = careProviderProfileService;
        _approvalService = approvalService;
        _availabilityTemplateService = availabilityTemplateService;
        _logger = logger;
        _mediator = mediator;
    }

    public async Task<Result<AuthResponse>> Handle(
        RegisterUserCommand request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            _logger.LogInformation("Registering user with email: {Email}", request.Email);

            // Normalize phone number
            string? normalizedPhoneNumber = null;
            if (!string.IsNullOrWhiteSpace(request.PhoneNumber))
            {
                try
                {
                    normalizedPhoneNumber = PhoneNumber.Create(request.PhoneNumber, "US");
                }
                catch (FormatException ex)
                {
                    _logger.LogError(
                        ex,
                        "Invalid phone number format for email: {Email}",
                        request.Email
                    );
                    return Result.Failure<AuthResponse>(
                        Error.Validation("Invalid phone number format.")
                    );
                }
            }

            // Register user
            var result = await _authService.RegisterAsync(
                request.Email,
                request.Password,
                normalizedPhoneNumber,
                request.IsCareProvider
            );

            if (result.IsFailure)
            {
                _logger.LogError(
                    "Failed to register user with email: {Email}: {Error}",
                    request.Email,
                    result.Error.Message
                );
                return Result.Failure<AuthResponse>(result.Error);
            }

            var userId = result.Value;

            // Assign role based on IsCareProvider
            var role = request.IsCareProvider ? "CareProvider" : "Client";
            var roleResult = await _authService.AddToRoleAsync(userId.ToString(), role);
            if (roleResult.IsFailure)
            {
                _logger.LogError(
                    "Failed to assign {Role} role to user {Email}: {Error}",
                    role,
                    request.Email,
                    roleResult.Error.Message
                );
                return Result.Failure<AuthResponse>(roleResult.Error);
            }

            var userProfileResult = await _authService.CreateUserProfileAsync(
                request.Email,
                request.FirstName,
                request.LastName,
                normalizedPhoneNumber
            );

            if (userProfileResult.IsFailure)
            {
                _logger.LogError(
                    "Failed to create user profile for {Email}: {Error}",
                    request.Email,
                    userProfileResult.Error.Message
                );
                return Result.Failure<AuthResponse>(userProfileResult.Error);
            }

            if (request.IsCareProvider)
            {
                var providerProfileResult = await _careProviderProfileService.CreateAsync(
                    new CareProviderProfile
                    {
                        UserId = userId,
                        YearsExperience = 0,
                        VerificationStatus = VerificationStatus.Pending,
                    }
                );

                if (providerProfileResult.IsFailure)
                {
                    _logger.LogError(
                        "Failed to create care provider profile for {Email}: {Error}",
                        request.Email,
                        providerProfileResult.Error.Message
                    );
                    return Result.Failure<AuthResponse>(providerProfileResult.Error);
                }

                var defaultTemplateCreationResult =
                    await _availabilityTemplateService.CreateDefaultAvailabilityTemplateAsync(
                        providerProfileResult.Value
                    );

                if (defaultTemplateCreationResult.IsFailure)
                {
                    return Result.Failure<AuthResponse>(defaultTemplateCreationResult.Error);
                }

                var approvalData = JsonSerializer.Serialize(
                    new
                    {
                        Email = request.Email,
                        PhoneNumber = normalizedPhoneNumber,
                        ProfileId = providerProfileResult.Value,
                    }
                );

                var approvalResult = await _approvalService.CreateApprovalAsync(
                    userId,
                    ApprovalType.CareProviderVerification,
                    approvalData,
                    providerProfileResult.Value
                );

                if (approvalResult.IsFailure)
                {
                    _logger.LogError(
                        "Failed to create approval request for care provider {Email}: {Error}",
                        request.Email,
                        approvalResult.Error.Message
                    );
                }
                else
                {
                    _logger.LogInformation(
                        "Created approval request {ApprovalId} for care provider {Email}",
                        approvalResult.Value.Id,
                        request.Email
                    );
                }
            }

            var tokenResult = await _authService.AuthenticateAsync(request.Email, request.Password);
            if (tokenResult.IsFailure)
            {
                _logger.LogError(
                    "Failed to authenticate user after registration for {Email}: {Error}",
                    request.Email,
                    tokenResult.Error.Message
                );
                return Result.Failure<AuthResponse>(tokenResult.Error);
            }

            return Result.Success(tokenResult.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while registering the user.");
            return Result.Failure<AuthResponse>(
                Error.Internal("An unexpected error occurred during user registration.")
            );
        }
    }
}
