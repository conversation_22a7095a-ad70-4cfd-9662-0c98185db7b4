using FluentValidation;

public record CreateReviewRequest(string Notes, decimal Rating);

public class CreateReviewRequestValidator : AbstractValidator<CreateReviewRequest>
{
    public CreateReviewRequestValidator()
    {
        // Notes can be empty, but if provided, should only contain allowed characters
        RuleFor(x => x.Notes)
            .Matches(@"^[a-zA-Z0-9\s.,!]*$")
            .WithMessage(
                "Notes contain invalid characters. Only letters, numbers, spaces, and the characters '.', ',', '!' are allowed."
            );

        RuleFor(x => x.Rating)
            .NotNull()
            .WithMessage("Rating is required.")
            .GreaterThan(0)
            .WithMessage("Rating must be greater than 0.")
            .NotEmpty();
    }
}
