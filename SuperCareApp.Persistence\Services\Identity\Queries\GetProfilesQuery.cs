using System.Globalization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using SuperCareApp.Application.Common.Interfaces.Messages.Query;
using SuperCareApp.Application.Common.Models.Identity;
using SuperCareApp.Domain.Entities;
using SuperCareApp.Domain.Enums;

namespace SuperCareApp.Persistence.Services.Identity.Queries;

public record GetProfilesQuery(
    UserType UserType,
    ProfileListParams PaginationParams,
    string? SearchTerm = null
) : IQuery<Result<PagedProfileList>>;

internal sealed class GetProfilesQueryHandler
    : IQueryHandler<GetProfilesQuery, Result<PagedProfileList>>
{
    private readonly IUserProfileService _userProfileService;
    private readonly ICareProviderProfileService _careProviderProfileService;
    private readonly ApplicationDbContext _context;
    private readonly IWebHostEnvironment _environment;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IFileStorageService _fileStorageService;

    public GetProfilesQueryHandler(
        IUserProfileService userProfileService,
        ICareProviderProfileService careProviderProfileService,
        ApplicationDbContext context,
        IWebHostEnvironment environment,
        IHttpContextAccessor httpContextAccessor,
        IFileStorageService fileStorageService
    )
    {
        _userProfileService = userProfileService;
        _careProviderProfileService = careProviderProfileService;
        _context = context;
        _environment = environment;
        _httpContextAccessor = httpContextAccessor;
        _fileStorageService = fileStorageService;
    }

    public async Task<Result<PagedProfileList>> Handle(
        GetProfilesQuery request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            // Ensure valid pagination parameters
            if (request.PaginationParams.PageNumber < 1)
            {
                return Result.Failure<PagedProfileList>(
                    Error.BadRequest("Page number should be at least 1")
                );
            }

            switch (request.UserType)
            {
                case UserType.Client:
                    return await GetClientProfilesAsync(
                        request.PaginationParams,
                        request.SearchTerm,
                        cancellationToken
                    );

                case UserType.CareProvider:
                    return await GetCareProviderProfilesAsync(
                        request.PaginationParams,
                        request.SearchTerm,
                        cancellationToken
                    );

                default:
                    return Result.Failure<PagedProfileList>(
                        Error.BadRequest("Unsupported user type")
                    );
            }
        }
        catch (Exception ex)
        {
            return Result.Failure<PagedProfileList>(
                Error.Internal($"Error retrieving profiles: {ex.Message}")
            );
        }
    }

    private async Task<Result<PagedProfileList>> GetClientProfilesAsync(
        ProfileListParams parameters,
        string? searchTerm,
        CancellationToken cancellationToken
    )
    {
        try
        {
            // Start with a query for all non-deleted user profiles
            var query = _context
                .UserProfiles.Include(up => up.User)
                .Where(up =>
                    !up.IsDeleted
                    && up.User.EmailVerified
                    && !_context.CareProviderProfiles.Any(cp => cp.UserId == up.ApplicationUserId)
                );

            // Apply search term if provided
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                searchTerm = searchTerm.ToLower();
                query = query.Where(up =>
                    (up.FirstName != null && up.FirstName.ToLower().Contains(searchTerm))
                    || (up.LastName != null && up.LastName.ToLower().Contains(searchTerm))
                    || (up.PhoneNumber != null && up.PhoneNumber.Contains(searchTerm))
                    || (up.Gender != null && up.Gender.ToLower().Contains(searchTerm))
                );
            }

            // Apply sorting
            if (!string.IsNullOrWhiteSpace(parameters.SortBy))
            {
                switch (parameters.SortBy.ToLower())
                {
                    case "name":
                        query = parameters.SortDescending
                            ? query
                                .OrderByDescending(up => up.FirstName)
                                .ThenByDescending(up => up.LastName)
                            : query.OrderBy(up => up.FirstName).ThenBy(up => up.LastName);
                        break;
                    case "phonenumber":
                        query = parameters.SortDescending
                            ? query.OrderByDescending(up => up.PhoneNumber)
                            : query.OrderBy(up => up.PhoneNumber);
                        break;
                    case "gender":
                        query = parameters.SortDescending
                            ? query.OrderByDescending(up => up.Gender)
                            : query.OrderBy(up => up.Gender);
                        break;
                    case "dateofbirth":
                        query = parameters.SortDescending
                            ? query.OrderByDescending(up => up.DateOfBirth)
                            : query.OrderBy(up => up.DateOfBirth);
                        break;
                    default:
                        query = parameters.SortDescending
                            ? query.OrderByDescending(up => up.CreatedAt)
                            : query.OrderBy(up => up.CreatedAt);
                        break;
                }
            }
            else
            {
                // Default sorting by creation date
                query = parameters.SortDescending
                    ? query.OrderByDescending(up => up.CreatedAt)
                    : query.OrderBy(up => up.CreatedAt);
            }

            // Get total count for pagination
            var totalCount = await query.CountAsync(cancellationToken);

            // Calculate pagination values
            var totalPages = (int)Math.Ceiling(totalCount / (double)parameters.PageSize);
            var skip = (parameters.PageNumber - 1) * parameters.PageSize;

            // Apply pagination
            var pagedData = await query
                .Skip(skip)
                .Take(parameters.PageSize)
                .ToListAsync(cancellationToken);

            // Map to response objects
            var profiles = pagedData.Select(MapToProfileResponse).ToList();

            // Build paged response
            var result = new PagedProfileList
            {
                Profiles = profiles,
                PageNumber = parameters.PageNumber,
                PageSize = parameters.PageSize,
                TotalCount = totalCount,
                TotalPages = totalPages,
            };

            return Result.Success(result);
        }
        catch (Exception ex)
        {
            return Result.Failure<PagedProfileList>(
                Error.Internal($"Error retrieving client profiles: {ex.Message}")
            );
        }
    }

    private async Task<Result<PagedProfileList>> GetCareProviderProfilesAsync(
        ProfileListParams parameters,
        string? searchTerm,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var query = _context
                .CareProviderProfiles.Include(cp => cp.User)
                .Include(cp => cp.CareProviderCategories.Where(cpc => cpc.HourlyRate > 0))
                .ThenInclude(cpc => cpc.CareCategory)
                .Include(cp => cp.Availabilities)
                .ThenInclude(a => a.AvailabilitySlots)
                .Where(cp =>
                    !cp.IsDeleted
                    && cp.VerificationStatus == VerificationStatus.Verified
                    && cp.User.EmailVerified
                );

            // Apply filters if provided
            if (parameters.Filters != null)
            {
                // Filter by CategoryIds
                if (parameters.Filters.CategoryIds != null && parameters.Filters.CategoryIds.Any())
                {
                    query = query.Where(cp =>
                        cp.CareProviderCategories.Any(cpc =>
                            parameters.Filters.CategoryIds.Contains(cpc.CategoryId)
                        )
                    );
                }

                // Filter by Genders (from UserProfile)
                if (parameters.Filters.Genders != null && parameters.Filters.Genders.Any())
                {
                    var userProfileIds = _context
                        .UserProfiles.Where(up =>
                            up.Gender != null
                            && parameters.Filters.Genders.Contains(up.Gender.ToLower())
                        )
                        .Select(up => up.ApplicationUserId);
                    query = query.Where(cp => userProfileIds.Contains(cp.UserId));
                }

                // Filter by Experience
                if (parameters.Filters.MinExperience.HasValue)
                {
                    query = query.Where(cp =>
                        cp.YearsExperience >= parameters.Filters.MinExperience.Value
                    );
                }

                if (parameters.Filters.MaxExperience.HasValue)
                {
                    query = query.Where(cp =>
                        cp.YearsExperience <= parameters.Filters.MaxExperience.Value
                    );
                }

                // Filter by Price Range
                if (parameters.Filters.MinPrice.HasValue)
                {
                    query = query.Where(cp =>
                        cp.HourlyRate.HasValue && cp.HourlyRate >= parameters.Filters.MinPrice.Value
                    );
                }

                if (parameters.Filters.MaxPrice.HasValue)
                {
                    query = query.Where(cp =>
                        cp.HourlyRate.HasValue && cp.HourlyRate <= parameters.Filters.MaxPrice.Value
                    );
                }

                // Filter by Age (calculated from UserProfile.DateOfBirth)
                if (parameters.Filters.MinAge.HasValue || parameters.Filters.MaxAge.HasValue)
                {
                    var currentDate = DateTime.UtcNow; // June 16, 2025
                    var userProfileIds = _context
                        .UserProfiles.Where(up => up.DateOfBirth.HasValue)
                        .Select(up => new { up.ApplicationUserId, up.DateOfBirth });

                    if (parameters.Filters.MinAge.HasValue)
                    {
                        var minBirthDate = currentDate.AddYears(-parameters.Filters.MinAge.Value);
                        userProfileIds = userProfileIds.Where(up => up.DateOfBirth <= minBirthDate);
                    }

                    if (parameters.Filters.MaxAge.HasValue)
                    {
                        var maxBirthDate = currentDate.AddYears(
                            -(parameters.Filters.MaxAge.Value + 1)
                        );
                        userProfileIds = userProfileIds.Where(up => up.DateOfBirth > maxBirthDate);
                    }

                    query = query.Where(cp =>
                        userProfileIds.Select(up => up.ApplicationUserId).Contains(cp.UserId)
                    );
                }

                // Filter by Location (using UserAddress and Address)
                if (
                    parameters.Filters.LocationLat.HasValue
                    && parameters.Filters.LocationLong.HasValue
                    && parameters.Filters.DistanceRadius.HasValue
                )
                {
                    var userAddressIds = _context
                        .UserAddresses.Join(
                            _context.Addresses,
                            ua => ua.AddressId,
                            a => a.Id,
                            (ua, a) =>
                                new
                                {
                                    ua.UserId,
                                    a.Latitude,
                                    a.Longitude,
                                }
                        )
                        .Where(ua => ua.Latitude.HasValue && ua.Longitude.HasValue)
                        .Select(ua => new
                        {
                            ua.UserId,
                            Distance = CalculateDistance(
                                parameters.Filters.LocationLat.Value,
                                parameters.Filters.LocationLong.Value,
                                (double)ua.Latitude!.Value,
                                (double)ua.Longitude!.Value
                            ),
                        })
                        .Where(ua => ua.Distance <= parameters.Filters.DistanceRadius.Value)
                        .Select(ua => ua.UserId);

                    query = query.Where(cp => userAddressIds.Contains(cp.UserId));
                }

                // Availability
                if (
                    !string.IsNullOrWhiteSpace(parameters.Filters.Date)
                    || parameters.Filters.StartTime.HasValue
                    || parameters.Filters.EndTime.HasValue
                )
                {
                    DateOnly? parsedDate = null;

                    if (!string.IsNullOrWhiteSpace(parameters.Filters.Date))
                    {
                        if (
                            !DateOnly.TryParseExact(
                                parameters.Filters.Date,
                                "yyyy-MM-dd",
                                CultureInfo.InvariantCulture,
                                DateTimeStyles.None,
                                out var date
                            )
                        )
                        {
                            return Result.Failure<PagedProfileList>(
                                Error.BadRequest(
                                    "Invalid date format. Use YYYY-MM-DD (e.g., 2025-06-24)"
                                )
                            );
                        }

                        parsedDate = date;
                    }

                    query = query.Where(cp =>
                        cp.Availabilities.Any(a =>
                            a.IsAvailable
                            && (
                                !parsedDate.HasValue
                                || a.DayOfWeek == parsedDate.Value.DayOfWeek.ToString()
                            )
                            && a.AvailabilitySlots.Any(slot =>
                                (
                                    !parameters.Filters.StartTime.HasValue
                                    || slot.StartTime >= parameters.Filters.StartTime.Value
                                )
                                && (
                                    !parameters.Filters.EndTime.HasValue
                                    || slot.EndTime <= parameters.Filters.EndTime.Value
                                )
                            )
                        )
                    );
                }
            }

            // Apply search term if provided
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                searchTerm = searchTerm.ToLower();
                var userProfileIds = _context
                    .UserProfiles.Where(up =>
                        // Individual field checks
                        (up.FirstName != null && up.FirstName.ToLower().Contains(searchTerm))
                        || (up.LastName != null && up.LastName.ToLower().Contains(searchTerm))
                        || (up.PhoneNumber != null && up.PhoneNumber.Contains(searchTerm))
                        || (up.Gender != null && up.Gender.ToLower().Contains(searchTerm))
                        // Full name check
                        || (
                            up.FirstName != null
                            && up.LastName != null
                            && (up.FirstName + " " + up.LastName).ToLower().Contains(searchTerm)
                        )
                        // Optional: Reverse order for flexibility (e.g., "Garcia Maria")
                        || (
                            up.FirstName != null
                            && up.LastName != null
                            && (up.LastName + " " + up.FirstName).ToLower().Contains(searchTerm)
                        )
                    )
                    .Select(up => up.ApplicationUserId);

                query = query.Where(cp =>
                    userProfileIds.Contains(cp.UserId)
                    || (cp.Bio != null && cp.Bio.ToLower().Contains(searchTerm))
                    || cp.YearsExperience.ToString().Contains(searchTerm)
                    || cp.Rating.ToString().Contains(searchTerm)
                    || cp.HourlyRate.ToString().Contains(searchTerm)
                );
            }

            // Apply sorting
            if (!string.IsNullOrWhiteSpace(parameters.SortBy))
            {
                switch (parameters.SortBy.ToLower())
                {
                    case "yearsexperience":
                        query = parameters.SortDescending
                            ? query.OrderByDescending(cp => cp.YearsExperience)
                            : query.OrderBy(cp => cp.YearsExperience);
                        break;
                    case "rating":
                        query = parameters.SortDescending
                            ? query.OrderByDescending(cp => cp.Rating)
                            : query.OrderBy(cp => cp.Rating);
                        break;
                    case "hourlyrate":
                        query = parameters.SortDescending
                            ? query.OrderByDescending(cp => cp.HourlyRate)
                            : query.OrderBy(cp => cp.HourlyRate);
                        break;
                    default:
                        query = parameters.SortDescending
                            ? query.OrderByDescending(cp => cp.CreatedAt)
                            : query.OrderBy(cp => cp.CreatedAt);
                        break;
                }
            }
            else
            {
                query = parameters.SortDescending
                    ? query.OrderByDescending(cp => cp.CreatedAt)
                    : query.OrderBy(cp => cp.CreatedAt);
            }

            // Get total count for pagination
            var totalCount = await query.CountAsync(cancellationToken);

            // Calculate pagination values
            var totalPages = (int)Math.Ceiling(totalCount / (double)parameters.PageSize);
            var skip = (parameters.PageNumber - 1) * parameters.PageSize;

            // Apply pagination
            var pagedData = await query
                .Skip(skip)
                .Take(parameters.PageSize)
                .ToListAsync(cancellationToken);

            // Map to response objects
            var profiles = new List<ProfileResponse>();
            foreach (var profile in pagedData)
            {
                profiles.Add(MapToProfileResponse(profile));
            }

            // Build paged response
            var result = new PagedProfileList
            {
                Profiles = profiles,
                PageNumber = parameters.PageNumber,
                PageSize = parameters.PageSize,
                TotalCount = totalCount,
                TotalPages = totalPages,
            };

            return Result.Success(result);
        }
        catch (Exception ex)
        {
            return Result.Failure<PagedProfileList>(
                Error.Internal($"Error retrieving care provider profiles: {ex.Message}")
            );
        }
    }

    // Helper method for distance calculation using Haversine formula
    private double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        const double R = 6371; // Earth's radius in kilometers
        var dLat = ToRadians(lat2 - lat1);
        var dLon = ToRadians(lon2 - lon1);
        var a =
            Math.Sin(dLat / 2) * Math.Sin(dLat / 2)
            + Math.Cos(ToRadians(lat1))
                * Math.Cos(ToRadians(lat2))
                * Math.Sin(dLon / 2)
                * Math.Sin(dLon / 2);
        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
        return R * c;
    }

    private double ToRadians(double degrees)
    {
        return degrees * Math.PI / 180;
    }

    private ProfileResponse MapToProfileResponse(UserProfile profile)
    {
        // Get the file URL if the image path exists
        string? profilePictureUrl = null;
        if (!string.IsNullOrEmpty(profile.ImagePath))
        {
            // Use the file storage service to get the URL from the relative path
            profilePictureUrl = _fileStorageService.GetFileUrl(profile.ImagePath);
        }

        return new ProfileResponse
        {
            Id = profile.Id,
            UserId = profile.ApplicationUserId,
            Email = profile.User.Email ?? string.Empty,
            FirstName = profile.FirstName ?? string.Empty,
            LastName = profile.LastName ?? string.Empty,
            PhoneNumber = profile.PhoneNumber ?? string.Empty,
            Gender = profile.Gender ?? string.Empty,
            DateOfBirth = profile.DateOfBirth,
            YearsExperience = 0, // Default for client,
            ProfilePictureUrl = profilePictureUrl,
            Country = profile.Country,
        };
    }

    private ProfileResponse MapToProfileResponse(CareProviderProfile profile)
    {
        // Get user profile in a single query with null checks
        var userProfile = _context
            .UserProfiles.AsNoTracking()
            .FirstOrDefault(up => up.ApplicationUserId == profile.UserId);

        // Simplify null checks with null-coalescing and conditional operators
        var profilePictureUrl = !string.IsNullOrEmpty(userProfile?.ImagePath)
            ? _fileStorageService.GetFileUrl(userProfile.ImagePath)
            : null;

        // Transform categories using LINQ projection
        var careCategories =
            profile
                .CareProviderCategories?.Select(cpc => new CategoryInfo
                {
                    Id = cpc.CategoryId,
                    Name = cpc.CareCategory?.Name ?? string.Empty,
                    HourlyRate = cpc.HourlyRate,
                    ExperienceLevel = cpc.ExperienceYears,
                })
                .ToList() ?? new List<CategoryInfo>();

        return new ProfileResponse
        {
            Id = profile.Id,
            UserId = profile.UserId,
            Email = profile.User?.Email ?? string.Empty,
            ProviderId = profile.Id,
            FirstName = userProfile?.FirstName ?? string.Empty,
            LastName = userProfile?.LastName ?? string.Empty,
            PhoneNumber = userProfile?.PhoneNumber ?? string.Empty,
            Gender = userProfile?.Gender ?? string.Empty,
            DateOfBirth = userProfile?.DateOfBirth,
            YearsExperience = profile.YearsExperience,
            WorkingHours = profile.WorkingHours,
            Categories = careCategories,
            ProfilePictureUrl = profilePictureUrl,
        };
    }
}
