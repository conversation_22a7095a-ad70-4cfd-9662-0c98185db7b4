using FluentValidation.TestHelper;
using SuperCareApp.Application.Common.Models.Categories;

namespace SuperCareApp.Persistence.Test.Categories;

public class CareCategoryValidationTests
{
    private readonly CreateCareCategoryRequestValidator _createValidator;
    private readonly UpdateCareCategoryRequestValidation _updateValidator;

    public CareCategoryValidationTests()
    {
        _createValidator = new CreateCareCategoryRequestValidator();
        _updateValidator = new UpdateCareCategoryRequestValidation();
    }

    #region CreateCareCategoryRequest Validation Tests

    [Fact]
    public void CreateCareCategoryRequest_WithValidIconAndColor_ShouldPassValidation()
    {
        // Arrange
        var request = new CreateCareCategoryRequest
        {
            Name = "Test Category",
            Description = "Test Description",
            IsActive = true,
            PlatformFee = 10.00m,
            Icon = "fas fa-heart",
            Color = "#FF5733",
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void CreateCareCategoryRequest_WithNullIconAndColor_ShouldPassValidation()
    {
        // Arrange
        var request = new CreateCareCategoryRequest
        {
            Name = "Test Category",
            Description = "Test Description",
            IsActive = true,
            PlatformFee = 10.00m,
            Icon = null,
            Color = null,
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void CreateCareCategoryRequest_WithTooLongIcon_ShouldFailValidation()
    {
        // Arrange
        var longIcon = new string('a', 256); // 256 characters, exceeds 255 limit
        var request = new CreateCareCategoryRequest
        {
            Name = "Test Category",
            Icon = longIcon,
            Color = "#FF5733",
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result
            .ShouldHaveValidationErrorFor(x => x.Icon)
            .WithErrorMessage("Icon cannot exceed 255 characters.");
    }

    [Theory]
    [InlineData("FF5733")] // Missing #
    [InlineData("#FF573")] // Too short
    [InlineData("#FF57333")] // Too long
    [InlineData("#GG5733")] // Invalid hex characters
    [InlineData("red")] // Not hex format
    [InlineData("#FF5733X")] // Too long with invalid character
    public void CreateCareCategoryRequest_WithInvalidColorFormat_ShouldFailValidation(
        string invalidColor
    )
    {
        // Arrange
        var request = new CreateCareCategoryRequest
        {
            Name = "Test Category",
            Icon = "fas fa-heart",
            Color = invalidColor,
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result
            .ShouldHaveValidationErrorFor(x => x.Color)
            .WithErrorMessage("Color must be a valid hex color code (e.g., #FF5733).");
    }

    [Theory]
    [InlineData("#000000")] // Black
    [InlineData("#FFFFFF")] // White
    [InlineData("#FF0000")] // Red
    [InlineData("#00FF00")] // Green
    [InlineData("#0000FF")] // Blue
    [InlineData("#123ABC")] // Mixed numbers and letters
    public void CreateCareCategoryRequest_WithValidColorFormats_ShouldPassValidation(
        string validColor
    )
    {
        // Arrange
        var request = new CreateCareCategoryRequest
        {
            Name = "Test Category",
            Icon = "fas fa-heart",
            Color = validColor,
        };

        // Act & Assert
        var result = _createValidator.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Color);
    }

    #endregion

    #region UpdateCareCategoryRequest Validation Tests

    [Fact]
    public void UpdateCareCategoryRequest_WithValidIconAndColor_ShouldPassValidation()
    {
        // Arrange
        var request = new UpdateCareCategoryRequest
        {
            Name = "Updated Category",
            Description = "Updated Description",
            IsActive = false,
            PlatformFee = 15.00m,
            Icon = "fas fa-star",
            Color = "#00FF00",
        };

        // Act & Assert
        var result = _updateValidator.TestValidate(request);
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void UpdateCareCategoryRequest_WithNullIconAndColor_ShouldPassValidation()
    {
        // Arrange
        var request = new UpdateCareCategoryRequest
        {
            Name = "Updated Category",
            Icon = null,
            Color = null,
        };

        // Act & Assert
        var result = _updateValidator.TestValidate(request);
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Fact]
    public void UpdateCareCategoryRequest_WithTooLongIcon_ShouldFailValidation()
    {
        // Arrange
        var longIcon = new string('b', 256); // 256 characters, exceeds 255 limit
        var request = new UpdateCareCategoryRequest
        {
            Name = "Updated Category",
            Icon = longIcon,
            Color = "#00FF00",
        };

        // Act & Assert
        var result = _updateValidator.TestValidate(request);
        result
            .ShouldHaveValidationErrorFor(x => x.Icon)
            .WithErrorMessage("Icon cannot exceed 255 characters.");
    }

    [Theory]
    [InlineData("00FF00")] // Missing #
    [InlineData("#00FF0")] // Too short
    [InlineData("#00FF000")] // Too long
    [InlineData("#GGFF00")] // Invalid hex characters
    [InlineData("green")] // Not hex format
    public void UpdateCareCategoryRequest_WithInvalidColorFormat_ShouldFailValidation(
        string invalidColor
    )
    {
        // Arrange
        var request = new UpdateCareCategoryRequest
        {
            Name = "Updated Category",
            Icon = "fas fa-star",
            Color = invalidColor,
        };

        // Act & Assert
        var result = _updateValidator.TestValidate(request);
        result
            .ShouldHaveValidationErrorFor(x => x.Color)
            .WithErrorMessage("Color must be a valid hex color code (e.g., #FF5733).");
    }

    [Theory]
    [InlineData("#FFFF00")] // Yellow
    [InlineData("#FF00FF")] // Magenta
    [InlineData("#00FFFF")] // Cyan
    [InlineData("#800080")] // Purple
    [InlineData("#FFA500")] // Orange
    [InlineData("#A1B2C3")] // Custom color
    public void UpdateCareCategoryRequest_WithValidColorFormats_ShouldPassValidation(
        string validColor
    )
    {
        // Arrange
        var request = new UpdateCareCategoryRequest
        {
            Name = "Updated Category",
            Icon = "fas fa-star",
            Color = validColor,
        };

        // Act & Assert
        var result = _updateValidator.TestValidate(request);
        result.ShouldNotHaveValidationErrorFor(x => x.Color);
    }

    [Fact]
    public void UpdateCareCategoryRequest_WithEmptyStringIconAndColor_ShouldPassValidation()
    {
        // Arrange
        var request = new UpdateCareCategoryRequest
        {
            Name = "Updated Category",
            Icon = "",
            Color = "",
        };

        // Act & Assert
        var result = _updateValidator.TestValidate(request);
        // Empty strings should pass validation as they're treated as valid (though they might be converted to null in business logic)
        result.ShouldNotHaveValidationErrorFor(x => x.Icon);
        result.ShouldHaveValidationErrorFor(x => x.Color); // Empty string should fail color validation
    }

    [Theory]
    [InlineData("fas fa-heart")]
    [InlineData("far fa-star")]
    [InlineData("fab fa-facebook")]
    [InlineData("material-icons")]
    [InlineData("icon-custom")]
    [InlineData("https://example.com/icon.svg")]
    [InlineData("<svg>...</svg>")]
    public void CreateAndUpdateCareCategoryRequest_WithVariousIconFormats_ShouldPassValidation(
        string iconValue
    )
    {
        // Test CreateCareCategoryRequest
        var createRequest = new CreateCareCategoryRequest
        {
            Name = "Test Category",
            Icon = iconValue,
            Color = "#FF5733",
        };

        var createResult = _createValidator.TestValidate(createRequest);
        createResult.ShouldNotHaveValidationErrorFor(x => x.Icon);

        // Test UpdateCareCategoryRequest
        var updateRequest = new UpdateCareCategoryRequest
        {
            Name = "Updated Category",
            Icon = iconValue,
            Color = "#FF5733",
        };

        var updateResult = _updateValidator.TestValidate(updateRequest);
        updateResult.ShouldNotHaveValidationErrorFor(x => x.Icon);
    }

    #endregion
}
